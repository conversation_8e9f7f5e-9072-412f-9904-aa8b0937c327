///
/// @file Antlr4CSTVisitor.cpp
/// @brief Antlr4的具体语法树的遍历产生AST
/// <AUTHOR> (<EMAIL>)
/// @version 1.1
/// @date 2024-11-23
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-09-29 <td>1.0     <td>zenglj  <td>新建
/// <tr><td>2024-11-23 <td>1.1     <td>zenglj  <td>表达式版增强
/// </table>
///

#include <string>

#include "Antlr4CSTVisitor.h"
#include "AST.h"
#include "Type.h"
#include "AttrType.h"

#define Instanceof(res, type, var) auto res = dynamic_cast<type>(var)

/// @brief 构造函数
MiniCCSTVisitor::MiniCCSTVisitor()
{}

/// @brief 析构函数
MiniCCSTVisitor::~MiniCCSTVisitor()
{}

/// @brief 遍历CST产生AST
/// @param root CST语法树的根结点
/// @return AST的根节点
ast_node * MiniCCSTVisitor::run(MiniCParser::CompileUnitContext * root)
{
    return std::any_cast<ast_node *>(visitCompileUnit(root));
}

/// @brief 非终结运算符compileUnit的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitCompileUnit(MiniCParser::CompileUnitContext * ctx)
{
    // compileUnit: (funcDef | varDecl)* EOF

    // 请注意这里必须先遍历全局变量后遍历函数。肯定可以确保全局变量先声明后使用的规则，但有些情况却不能检查出。
    // 事实上可能函数A后全局变量B后函数C，这时在函数A中是不能使用变量B的，需要报语义错误，但目前的处理不会。
    // 因此在进行语义检查时，可能追加检查行号和列号，如果函数的行号/列号在全局变量的行号/列号的前面则需要报语义错误
    // TODO 请追加实现。

    ast_node * temp_node;
    ast_node * compileUnitNode = create_contain_node(ast_operator_type::AST_OP_COMPILE_UNIT);

    // 可能多个变量，因此必须循环遍历
    for (auto varCtx: ctx->varDecl()) {

        // 变量函数定义
        temp_node = std::any_cast<ast_node *>(visitVarDecl(varCtx));
        (void) compileUnitNode->insert_son_node(temp_node);
    }

    // 可能有多个函数，因此必须循环遍历
    for (auto funcCtx: ctx->funcDef()) {

        // 变量函数定义
        temp_node = std::any_cast<ast_node *>(visitFuncDef(funcCtx));
        (void) compileUnitNode->insert_son_node(temp_node);
    }

    return compileUnitNode;
}

/// @brief 函数定义节点的CST到AST转换
/// @param ctx 函数定义的CST上下文
/// @return 函数定义的AST节点
/// 实验七：支持int/void返回类型和多个形参的函数定义
std::any MiniCCSTVisitor::visitFuncDef(MiniCParser::FuncDefContext *ctx)
{
    // 实验七：解析函数返回类型（int/void）
    type_attr funcReturnType = ctx->T_INT() ?
        type_attr{BasicType::TYPE_INT, (int64_t)ctx->T_INT()->getSymbol()->getLine()} :
        ctx->T_VOID() ?
        type_attr{BasicType::TYPE_VOID, (int64_t)ctx->T_VOID()->getSymbol()->getLine()} :
        throw std::runtime_error("Function return type must be int or void");

    // 提取函数名和位置信息
    char *id = strdup(ctx->T_ID()->getText().c_str());
    var_id_attr funcId{id, (int64_t)ctx->T_ID()->getSymbol()->getLine()};

    // 实验七：处理形参列表（支持多参数）
    ast_node *formalParamsNode = ctx->funcParamList() ?
        std::any_cast<ast_node *>(visitFuncParamList(ctx->funcParamList())) : nullptr;

    // 处理函数体并构建AST节点
    ast_node *blockNode = std::any_cast<ast_node *>(visitBlock(ctx->block()));
    return create_func_def(funcReturnType, funcId, blockNode, formalParamsNode);
}
/// @brief 非终结符 funcParamList 的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitFuncParamList(MiniCParser::FuncParamListContext *ctx)
{
    //实验七：形参列表处理（支持多参数）
    ast_node *paramListNode = new ast_node(ast_operator_type::AST_OP_FUNC_FORMAL_PARAMS);

    //实验七：C++范围for循环优化遍历
    for (const auto &paramCtx : ctx->funcParam()) {
        paramListNode->sons.push_back(std::any_cast<ast_node *>(visitFuncParam(paramCtx)));
    }

    return paramListNode;
}
std::any MiniCCSTVisitor::visitFuncParam(MiniCParser::FuncParamContext *ctx)
{
    //实验八：单个形参处理（类型+名称+数组维度）
    type_attr paramType{BasicType::TYPE_INT, (int64_t)ctx->basicType()->start->getLine()};

    // 提取参数标识符
    char *id = strdup(ctx->T_ID()->getText().c_str());

    // 创建形参节点并设置属性
    ast_node *paramNode = new ast_node(ast_operator_type::AST_OP_FUNC_FORMAL_PARAM);
    paramNode->name = std::string(id);

    // 实验八：处理数组形参的维度信息
    // funcParam: basicType T_ID (T_L_BRACKET T_R_BRACKET)* (T_L_BRACKET expr T_R_BRACKET)*;

    // 检查是否有数组维度（包括空维度和有大小的维度）
    bool isArrayParam = false;
    std::vector<int32_t> dimensions;

    // 计算空维度的数量 (T_L_BRACKET T_R_BRACKET)*
    size_t emptyBracketPairs = 0;
    size_t totalBrackets = ctx->T_L_BRACKET().size();
    size_t exprCount = ctx->expr().size();
    emptyBracketPairs = totalBrackets - exprCount;

    if (emptyBracketPairs > 0 || exprCount > 0) {
        isArrayParam = true;

        // 对于函数参数，第一维可以是空的（如 int a[]），我们将其视为指针
        // 后续维度需要有具体大小

        if (emptyBracketPairs > 0) {
            // 第一维是空的，表示这是一个数组参数
            // 暂时使用基本类型，在IR生成阶段处理
            paramNode->type = typeAttr2Type(paramType);

            // 添加标记节点表示这是数组参数
            ast_node * arrayMarker = new ast_node(ast_operator_type::AST_OP_LEAF_LITERAL_UINT);
            arrayMarker->integer_val = 0; // 特殊值表示数组参数
            arrayMarker->name = "__ARRAY_PARAM_MARKER__"; // 特殊名称
            paramNode->insert_son_node(arrayMarker);
        }

        // 处理有大小的维度 (T_L_BRACKET expr T_R_BRACKET)*
        for (auto exprCtx : ctx->expr()) {
            ast_node * dim_node = std::any_cast<ast_node *>(visitExpr(exprCtx));
            if (dim_node) {
                paramNode->insert_son_node(dim_node);

                // 尝试获取维度大小（简化处理，假设是常量）
                if (dim_node->node_type == ast_operator_type::AST_OP_LEAF_LITERAL_UINT) {
                    dimensions.push_back(static_cast<int>(dim_node->integer_val));
                } else {
                    dimensions.push_back(-1); // 未知大小
                }
            }
        }
    }

    if (!isArrayParam) {
        // 普通参数
        paramNode->type = typeAttr2Type(paramType);
    }

    free(id);
    return paramNode;
}

/// @brief 非终结运算符block的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitBlock(MiniCParser::BlockContext * ctx)
{
    // 识别的文法产生式：block : T_L_BRACE blockItemList? T_R_BRACE';
    if (!ctx->blockItemList()) {
        // 语句块没有语句

        // 为了方便创建一个空的Block节点
        return create_contain_node(ast_operator_type::AST_OP_BLOCK);
    }

    // 语句块含有语句

    // 内部创建Block节点，并把语句加入，这里不需要创建Block节点
    return visitBlockItemList(ctx->blockItemList());
}

/// @brief 非终结运算符blockItemList的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitBlockItemList(MiniCParser::BlockItemListContext * ctx)
{
    // 识别的文法产生式：blockItemList : blockItem +;
    // 正闭包 循环 至少一个blockItem
    auto block_node = create_contain_node(ast_operator_type::AST_OP_BLOCK);

    for (auto blockItemCtx: ctx->blockItem()) {

        // 非终结符，需遍历
        auto blockItem = std::any_cast<ast_node *>(visitBlockItem(blockItemCtx));

        // 插入到块节点中
        (void) block_node->insert_son_node(blockItem);
    }

    return block_node;
}

///
/// @brief 非终结运算符blockItem的遍历
/// @param ctx CST上下文
///
std::any MiniCCSTVisitor::visitBlockItem(MiniCParser::BlockItemContext * ctx)
{
    // 识别的文法产生式：blockItem : statement | varDecl
    if (ctx->statement()) {
        // 语句识别

        return visitStatement(ctx->statement());
    } else if (ctx->varDecl()) {
        return visitVarDecl(ctx->varDecl());
    }

    return nullptr;
}

/// @brief 非终结运算符statement中的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitStatement(MiniCParser::StatementContext *ctx)
{
    // 识别的文法产生式：
    // statement:
    //     T_RETURN expr? T_SEMICOLON                             # returnStatement
    //     | lVal T_ASSIGN expr T_SEMICOLON                       # assignStatement
    //     | T_IF T_L_PAREN expr T_R_PAREN statement (T_ELSE statement)?  # ifStatement
    //     | T_WHILE T_L_PAREN expr T_R_PAREN statement           # whileStatement
    //     | block                                                # blockStatement
    //     | expr T_SEMICOLON                                     # exprStatement;

    if (Instanceof(returnCtx, MiniCParser::ReturnStatementContext*, ctx)) {
        return visitReturnStatement(returnCtx);
    } else if(Instanceof(assignCtx, MiniCParser::AssignStatementContext*, ctx)) {
        return visitAssignStatement(assignCtx);
    }
    else if (Instanceof(ifCtx, MiniCParser::IfStatementContext*, ctx)) {
        return visitIfStatement(ifCtx);
    }
    else if (Instanceof(whileCtx, MiniCParser::WhileStatementContext*, ctx)) {
        return visitWhileStatement(whileCtx);
    }
    else if (Instanceof(blockCtx, MiniCParser::BlockStatementContext*, ctx)) {
        return visitBlockStatement(blockCtx);
    }
    else if (Instanceof(exprCtx, MiniCParser::ExpressionStatementContext*, ctx)) {
        return visitExpressionStatement(exprCtx);
    }else if (Instanceof(breakCtx, MiniCParser::BreakStatementContext*, ctx)) {
        return visitBreakStatement(breakCtx);
    }else if (Instanceof(continueCtx, MiniCParser::ContinueStatementContext*, ctx)) {
        return visitContinueStatement(continueCtx);
    }else if (Instanceof(emptyCtx, MiniCParser::EmptyStatementContext*, ctx)) {
        return visitEmptyStatement(emptyCtx);
    }
    return nullptr;
}
std::any MiniCCSTVisitor::visitIfStatement(MiniCParser::IfStatementContext *ctx){
    // 条件表达式
    auto cond = std::any_cast<ast_node*>(visitExpr(ctx->expr()));

    // then 语句总是有的，取第0个statement
    auto thenStmt = std::any_cast<ast_node*>(visitStatement(ctx->statement(0)));

    ast_node* elseStmt = nullptr;
    // 如果有第二个statement，说明是 if-else
    if (ctx->statement().size() > 1) {
        elseStmt = std::any_cast<ast_node*>(visitStatement(ctx->statement(1)));
        return ast_node::New(ast_operator_type::AST_OP_IF, cond, thenStmt, elseStmt, nullptr);
    }
    else {
        return ast_node::New(ast_operator_type::AST_OP_IF, cond, thenStmt, nullptr);
    }
}

std::any MiniCCSTVisitor::visitWhileStatement(MiniCParser::WhileStatementContext *ctx) {
    // while (expr) statement
    ast_node *cond = std::any_cast<ast_node*>(visitExpr(ctx->expr()));
    ast_node *body = std::any_cast<ast_node*>(visitStatement(ctx->statement()));

    return ast_node::New(ast_operator_type::AST_OP_WHILE, cond, body, nullptr);
}
std::any MiniCCSTVisitor::visitBreakStatement(MiniCParser::BreakStatementContext *ctx) {
    return ast_node::New(ast_operator_type::AST_OP_BREAK,nullptr);
}

std::any MiniCCSTVisitor::visitContinueStatement(MiniCParser::ContinueStatementContext *ctx) {
    return ast_node::New(ast_operator_type::AST_OP_CONTINUE,nullptr);
}

std::any MiniCCSTVisitor::visitEmptyStatement(MiniCParser::EmptyStatementContext *ctx) {
    // 空语句，创建一个空的AST节点
    return ast_node::New(ast_operator_type::AST_OP_BLOCK, nullptr);
}
///
/// @brief 非终结运算符statement中的returnStatement的遍历
/// @param ctx CST上下文
///
std::any MiniCCSTVisitor::visitReturnStatement(MiniCParser::ReturnStatementContext * ctx)
{
    // 识别的文法产生式：returnStatement -> T_RETURN expr T_SEMICOLON

    // 非终结符，表达式expr遍历
    auto exprNode = std::any_cast<ast_node *>(visitExpr(ctx->expr()));

    // 创建返回节点，其孩子为Expr
    return create_contain_node(ast_operator_type::AST_OP_RETURN, exprNode);
}

/// @brief 非终结运算符expr的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitExpr(MiniCParser::ExprContext * ctx)
{
    // 识别产生式：expr: orExp;

    return visitOrExp(ctx->orExp());
}

std::any MiniCCSTVisitor::visitAssignStatement(MiniCParser::AssignStatementContext * ctx)
{
    // 识别文法产生式：assignStatement: lVal T_ASSIGN expr T_SEMICOLON

    // 赋值左侧左值Lval遍历产生节点
    auto lvalNode = std::any_cast<ast_node *>(visitLVal(ctx->lVal()));

    // 赋值右侧expr遍历
    auto exprNode = std::any_cast<ast_node *>(visitExpr(ctx->expr()));

    // 创建一个AST_OP_ASSIGN类型的中间节点，孩子为Lval和Expr
    return ast_node::New(ast_operator_type::AST_OP_ASSIGN, lvalNode, exprNode, nullptr);
}

std::any MiniCCSTVisitor::visitBlockStatement(MiniCParser::BlockStatementContext * ctx)
{
    // 识别文法产生式 blockStatement: block

    return visitBlock(ctx->block());
}
//代修改
std::any MiniCCSTVisitor::visitAddExp(MiniCParser::AddExpContext * ctx)
{
    // 识别的文法产生式：addExp: mulExp (addOp mulExp)*;

    if (ctx->addOp().empty()) {

        // 没有addOp运算符，则说明闭包识别为0，只识别了第一个非终结符mulExp
        return visitMulExp(ctx->mulExp()[0]);
    }

    ast_node *left, *right;

    // 存在addOp运算符，自
    auto opsCtxVec = ctx->addOp();

    // 有操作符，肯定会进循环，使得right设置正确的值
    for (int k = 0; k < (int) opsCtxVec.size(); k++) {

        // 获取运算符
        ast_operator_type op = std::any_cast<ast_operator_type>(visitAddOp(opsCtxVec[k]));

        if (k == 0) {

            // 左操作数
            left = std::any_cast<ast_node *>(visitMulExp(ctx->mulExp()[k]));
        }

        // 右操作数
        right = std::any_cast<ast_node *>(visitMulExp(ctx->mulExp()[k + 1]));

        // 新建结点作为下一个运算符的右操作符
        left = ast_node::New(op, left, right, nullptr);
    }

    return left;
}
/// @brief 非终结运算符addOp的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitAddOp(MiniCParser::AddOpContext * ctx)
{
    // 识别的文法产生式：addOp : T_ADD | T_SUB

    if (ctx->T_ADD()) {
        return ast_operator_type::AST_OP_ADD;
    } else {
        return ast_operator_type::AST_OP_SUB;
    }
}
/// @brief 乘除模表达式mulExp的遍历
/// @param ctx CST上下文
/// @return 乘除模表达式的AST节点
std::any MiniCCSTVisitor::visitMulExp(MiniCParser::MulExpContext * ctx)
{
    // 识别文法产生式：mulExp: unaryExp (mulOp unaryExp)*;
    if (ctx->mulOp().empty()) {
        // 没有mulOp运算符，则说明闭包识别为0，只识别了第一个非终结符unaryExp
        return visitUnaryExp(ctx->unaryExp()[0]);
    }

    // 创新：获取运算符和操作数列表，避免重复调用
    const auto &opsCtxVec = ctx->mulOp();
    const auto &unaryExpVec = ctx->unaryExp();

    // 初始化左操作数，避免在循环中特殊处理
    ast_node *left = std::any_cast<ast_node *>(visitUnaryExp(unaryExpVec[0]));

    // 使用基于索引的现代循环，更清晰的逻辑
    for (size_t k = 0; k < opsCtxVec.size(); ++k) {
        // 获取运算符类型
        ast_operator_type op = std::any_cast<ast_operator_type>(visitMulOp(opsCtxVec[k]));

        // 获取右操作数
        ast_node *right = std::any_cast<ast_node *>(visitUnaryExp(unaryExpVec[k + 1]));

        // 创建新节点作为下一次循环的左操作数（左结合）
        left = ast_node::New(op, left, right, nullptr);
    }

    return left;
}
/// @brief 乘除模运算符mulOp的遍历
/// @param ctx CST上下文
/// @return 对应的AST运算符类型
std::any MiniCCSTVisitor::visitMulOp(MiniCParser::MulOpContext * ctx)
{
    // 识别文法产生式：mulOp: T_DIV | T_MUL | T_MOD;
    if (ctx->T_DIV()) {
        return ast_operator_type::AST_OP_DIV;
    } else if (ctx->T_MUL()) {
        return ast_operator_type::AST_OP_MUL;
    } else {
        return ast_operator_type::AST_OP_MOD;
    }
}
std::any MiniCCSTVisitor::visitRelExp(MiniCParser::RelExpContext * ctx)
{
    //识别文法relExp: addExp(relOp addExp);
    if (ctx->relOp().empty()) {

        // 没有mulOp运算符，则说明闭包识别为0，只识别了第一个非终结符unaryExp
        return visitAddExp(ctx->addExp()[0]);
    }

    ast_node *left, *right;

    // 存在relOp运算符，自
    auto opsCtxVec = ctx->relOp();

    // 有操作符，肯定会进循环，使得right设置正确的值
    for (int k = 0; k < (int) opsCtxVec.size(); k++) {

        // 获取运算符
        ast_operator_type op = std::any_cast<ast_operator_type>(visitRelOp(opsCtxVec[k]));

        if (k == 0) {

            // 左操作数
            left = std::any_cast<ast_node *>(visitAddExp(ctx->addExp()[k]));
        }

        // 右操作数
        right = std::any_cast<ast_node *>(visitAddExp(ctx->addExp()[k + 1]));

        // 新建结点作为下一个运算符的右操作符
        left = ast_node::New(op, left, right, nullptr);
    }

    return left;
}
// 改
std::any MiniCCSTVisitor::visitRelOp(MiniCParser::RelOpContext * ctx)
{
    // 识别的文法产生式：REL_OP: LT|LE|GT|GE;
    if (ctx->LT()){
        return ast_operator_type::AST_OP_LT;
    } else if(ctx->LE()) {
        return ast_operator_type::AST_OP_LE;
    }else if(ctx->GT()){
		return ast_operator_type::AST_OP_GT;
    } else{
        return ast_operator_type::AST_OP_GE;
		}
}
// 访问相等表达式：eqlExp -> relExp (eqlOp relExp)*
std::any MiniCCSTVisitor::visitEqlExp(MiniCParser::EqlExpContext *ctx)
{
    // 如果没有等号类操作符，直接访问第一个子表达式
    if (ctx->eqlOp().empty()) {
        return visitRelExp(ctx->relExp()[0]);
    }

    // 预处理所有 relExp 子节点
    std::vector<ast_node*> relExpNodes;
    for (auto *relCtx : ctx->relExp()) {
        relExpNodes.push_back(std::any_cast<ast_node*>(visitRelExp(relCtx)));
    }

    // 获取操作符
    auto opsCtxVec = ctx->eqlOp();
    ast_node* result = relExpNodes[0];

    // 依次构建二元表达式树
    for (size_t i = 0; i < opsCtxVec.size(); ++i) {
        auto op = std::any_cast<ast_operator_type>(visitEqlOp(opsCtxVec[i]));
        ast_node* right = relExpNodes[i + 1];

        // 每次生成新的中间节点
        result = ast_node::New(op, result, right, nullptr);
    }

    return result;
}

std::any MiniCCSTVisitor::visitEqlOp(MiniCParser::EqlOpContext * ctx)
{
    // eqlOp: EQUALS | NOT_EQUALS;
    if (ctx->EQUALS()){
        return ast_operator_type::AST_OP_EQUALS;
    } else{
        return ast_operator_type::AST_OP_NOT_EQUALS;
    }
}
std::any MiniCCSTVisitor::visitAndExp(MiniCParser::AndExpContext *ctx) {
    auto exprs = ctx->eqlExp();
    ast_node *result = std::any_cast<ast_node *>(visitEqlExp(exprs[0]));

    for (size_t i = 1; i < exprs.size(); ++i) {
        auto right = std::any_cast<ast_node *>(visitEqlExp(exprs[i]));
        result = ast_node::New(ast_operator_type::AST_OP_AND, result, right, nullptr);
    }

    return result;
}


std::any MiniCCSTVisitor::visitOrExp(MiniCParser::OrExpContext *ctx) {
    auto exprs = ctx->andExp();
    ast_node *result = std::any_cast<ast_node *>(visitAndExp(exprs[0]));

    for (size_t i = 1; i < exprs.size(); ++i) {
        auto right = std::any_cast<ast_node *>(visitAndExp(exprs[i]));
        result = ast_node::New(ast_operator_type::AST_OP_OR, result, right, nullptr);
    }

    return result;
}



/// @brief 非终结运算符unaryOp的遍历
/// @param ctx CST上下文
std::any MiniCCSTVisitor::visitUnaryOp(MiniCParser::UnaryOpContext * ctx)
{
    // 识别的文法产生式：unaryOp: T_SUB | NOT;
    if (ctx->T_SUB()) {
        return ast_operator_type::AST_OP_NEG;
    } else {
        return ast_operator_type::AST_OP_NOT;
    }
}
// 修改部分
std::any MiniCCSTVisitor::visitUnaryExp(MiniCParser::UnaryExpContext * ctx)
{
    if (ctx->unaryOp()) {
        // unaryOp unaryExp
        auto op = std::any_cast<ast_operator_type>(visitUnaryOp(ctx->unaryOp()));
        // 获取子表达式：ctx->getRuleContext<UnaryExpContext>(0)
        MiniCParser::UnaryExpContext * inner = ctx->getRuleContext<MiniCParser::UnaryExpContext>(0);
        auto expr = std::any_cast<ast_node *>(visit(inner));
        return ast_node::New(op, expr, nullptr);
    }
    else if (ctx->primaryExp()) {
        // primaryExp
        return visitPrimaryExp(ctx->primaryExp());
    }
    else if (ctx->T_ID()) {
        //实验七：创新函数调用（支持多实参）
        ast_node * funcname_node = ast_node::New(ctx->T_ID()->getText(), (int64_t) ctx->T_ID()->getSymbol()->getLine());

        //实验七：创新使用条件表达式简化实参列表处理
        ast_node * paramListNode = ctx->realParamList() ?
            std::any_cast<ast_node *>(visitRealParamList(ctx->realParamList())) : nullptr;

        return create_func_call(funcname_node, paramListNode);
    }
        return nullptr;
}

//增加VisitintLiteral
std::any MiniCCSTVisitor::visitIntLiteral(MiniCParser::IntLiteralContext *ctx) {
    std::string text;
    int base = 10;

    if (ctx->T_DIGIT()) {
        text = ctx->T_DIGIT()->getText();
    } else if (ctx->T_OCT_LITERAL()) {
        text = ctx->T_OCT_LITERAL()->getText();
        base = 8;
    } else if (ctx->T_HEX_LITERAL()) {
        text = ctx->T_HEX_LITERAL()->getText();
        base = 16;
    }

    uint32_t val = 0;
    try {
        val = static_cast<uint32_t>(stoul(text, nullptr, base));
    } catch (...) {
        val = 0; // 默认值（可考虑报错）
    }

    int64_t lineNo = (int64_t) ctx->start->getLine();
    return ast_node::New(digit_int_attr{val, lineNo});
}

//截至

std::any MiniCCSTVisitor::visitPrimaryExp(MiniCParser::PrimaryExpContext * ctx)
{
    // 原：识别文法产生式 primaryExp: T_L_PAREN expr T_R_PAREN | T_DIGIT | lVal;
    // 现：识别文法产生式 primaryExp: T_L_PAREN expr T_R_PAREN | intLiteral | lVal;
    ast_node * node = nullptr;

    if (ctx->intLiteral()) {
        node = std::any_cast<ast_node *>(visitIntLiteral(ctx->intLiteral()));
    } else if (ctx->lVal()) {
        // 具有左值的表达式
        // 识别 primaryExp: lVal
        node = std::any_cast<ast_node *>(visitLVal(ctx->lVal()));
    } else if (ctx->expr()) {
        // 带有括号的表达式
        // primaryExp: T_L_PAREN expr T_R_PAREN
        node = std::any_cast<ast_node *>(visitExpr(ctx->expr()));
    }

    return node;
}

std::any MiniCCSTVisitor::visitLVal(MiniCParser::LValContext * ctx)
{
    // 识别文法产生式：lVal: T_ID (T_L_BRACKET expr T_R_BRACKET)*;
    // 获取ID的名字
    auto varId = ctx->T_ID()->getText();

    // 获取行号
    int64_t lineNo = (int64_t) ctx->T_ID()->getSymbol()->getLine();

    // 创建基础变量节点
    ast_node * base_node = ast_node::New(varId, lineNo);

    // 如果没有数组索引，直接返回变量节点
    if (ctx->expr().empty()) {
        return base_node;
    }

    // 处理数组访问：收集所有索引表达式
    std::vector<ast_node *> indices;
    for (auto exprCtx : ctx->expr()) {
        indices.push_back(std::any_cast<ast_node *>(visitExpr(exprCtx)));
    }

    // 使用辅助函数处理多维数组访问
    return processArrayAccess(base_node, indices);
}

std::any MiniCCSTVisitor::visitVarDecl(MiniCParser::VarDeclContext * ctx)
{
    // varDecl: basicType varDef (T_COMMA varDef)* T_SEMICOLON;

    // 声明语句节点
    ast_node * stmt_node = create_contain_node(ast_operator_type::AST_OP_DECL_STMT);

    // 类型节点
    type_attr typeAttr = std::any_cast<type_attr>(visitBasicType(ctx->basicType()));

    for (auto & varCtx: ctx->varDef()) {
        // 获取变量定义节点（可能是普通变量或数组）
        ast_node * var_def_node = std::any_cast<ast_node *>(visitVarDef(varCtx));

        // 创建类型节点
        ast_node * type_node = create_type_node(typeAttr);

        // 检查是否是数组定义
        if (var_def_node->node_type == ast_operator_type::AST_OP_VAR_DECL && var_def_node->sons.size() > 1) {
            // 这是数组定义，保持原有结构但添加类型信息
            var_def_node->type = typeAttr2Type(typeAttr);
            (void) stmt_node->insert_son_node(var_def_node);
        } else {
            // 普通变量声明
            ast_node* decl_node = ast_node::New(ast_operator_type::AST_OP_VAR_DECL, type_node, var_def_node, nullptr);
            (void) stmt_node->insert_son_node(decl_node);
        }
    }

    return stmt_node;
}

std::any MiniCCSTVisitor::visitVarDef(MiniCParser::VarDefContext * ctx)
{
    // varDef: T_ID (T_L_BRACKET expr T_R_BRACKET)* (T_ASSIGN expr)?;

    auto varId = ctx->T_ID()->getText();

    // 获取行号
    int64_t lineNo = (int64_t) ctx->T_ID()->getSymbol()->getLine();

    // 创建变量名节点
    ast_node * id_node = ast_node::New(varId, lineNo);

    // 如果有数组维度，需要将维度信息附加到节点上
    // 这里我们创建一个包含变量名和维度信息的节点
    if (!ctx->expr().empty()) {
        // 这是数组定义，创建一个包含维度信息的节点
        ast_node * array_def_node = create_contain_node(ast_operator_type::AST_OP_VAR_DECL);
        array_def_node->name = varId;
        array_def_node->line_no = lineNo;

        // 添加变量名作为第一个子节点
        array_def_node->insert_son_node(id_node);

        // 添加所有维度表达式作为子节点
        for (auto exprCtx : ctx->expr()) {
            ast_node * dim_node = std::any_cast<ast_node *>(visitExpr(exprCtx));
            array_def_node->insert_son_node(dim_node);
        }

        return array_def_node;
    }

    return id_node;
}

std::any MiniCCSTVisitor::visitBasicType(MiniCParser::BasicTypeContext * ctx)
{
    // basicType: T_INT;
    type_attr attr{BasicType::TYPE_VOID, -1};
    if (ctx->T_INT()) {
        attr.type = BasicType::TYPE_INT;
        attr.lineno = (int64_t) ctx->T_INT()->getSymbol()->getLine();
    }

    return attr;
}

std::any MiniCCSTVisitor::visitRealParamList(MiniCParser::RealParamListContext * ctx)
{
    //实验七：创新实参列表处理（支持多实参）
    auto paramListNode = create_contain_node(ast_operator_type::AST_OP_FUNC_REAL_PARAMS);

    //实验七：创新使用现代C++范围for循环和链式调用
    for (const auto &paramCtx : ctx->expr()) {
        paramListNode->insert_son_node(std::any_cast<ast_node *>(visitExpr(paramCtx)));
    }

    return paramListNode;
}

std::any MiniCCSTVisitor::visitExpressionStatement(MiniCParser::ExpressionStatementContext * ctx)
{
    // 识别文法产生式  expr ? T_SEMICOLON #expressionStatement;
    if (ctx->expr()) {
        // 表达式语句

        // 遍历expr非终结符，创建表达式节点后返回
        return visitExpr(ctx->expr());
    } else {
        // 空语句

        // 直接返回空指针，需要再把语句加入到语句块时要注意判断，空语句不要加入
        return nullptr;
    }
}

///
/// @brief 处理数组访问的辅助函数，实验八新增
/// @param base_node 基础节点（变量或已有的数组访问）
/// @param indices 索引表达式列表
/// @return ast_node* 数组访问节点
///
ast_node * MiniCCSTVisitor::processArrayAccess(ast_node * base_node, const std::vector<ast_node *> & indices)
{
    ast_node * current = base_node;

    // 逐层构建数组访问节点
    for (ast_node * index : indices) {
        current = create_array_access_node(current, index);
    }

    return current;
}