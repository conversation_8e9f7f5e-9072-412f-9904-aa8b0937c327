declare i32 @g
define i32 @func(i32%t0)
{
	declare i32 %l1 ; 1:n
	declare i32 %l2
	declare i32 %t3
	entry
	%l1 = %t0
	%t3 = add @g,%l1
	@g = %t3
	call void @putint(i32 @g)
	%l2 = @g
	br label .L4
.L4:
	exit %l2
}
define i32 @main()
{
	declare i32 %l0
	declare i32 %l1 ; 1:i
	declare i32 %t2
	declare i1 %t3
	declare i32 %t5
	declare i32 %t9
	declare i1 %t10
	declare i32 %t12
	declare i32 %t16
	declare i1 %t17
	declare i32 %t19
	declare i32 %t23
	declare i1 %t24
	declare i32 %t26
	declare i32 %t30
	declare i32 %t32
	entry
	%l0 = 0
	%t2 = call i32 @getint()
	%l1 = %t2
	%t3 = icmp gt %l1,10
	bc %t3, label .L4, label .L7
.L4:
	%t5 = call i32 @func(i32 %l1)
	bc %t5, label .L6, label .L7
.L6:
	%l1 = 1
	br label .L8
.L7:
	%l1 = 0
	br label .L8
.L8:
	%t9 = call i32 @getint()
	%l1 = %t9
	%t10 = icmp gt %l1,11
	bc %t10, label .L11, label .L14
.L11:
	%t12 = call i32 @func(i32 %l1)
	bc %t12, label .L13, label .L14
.L13:
	%l1 = 1
	br label .L15
.L14:
	%l1 = 0
	br label .L15
.L15:
	%t16 = call i32 @getint()
	%l1 = %t16
	%t17 = icmp le %l1,99
	bc %t17, label .L20, label .L18
.L18:
	%t19 = call i32 @func(i32 %l1)
	bc %t19, label .L20, label .L21
.L20:
	%l1 = 1
	br label .L22
.L21:
	%l1 = 0
	br label .L22
.L22:
	%t23 = call i32 @getint()
	%l1 = %t23
	%t24 = icmp le %l1,100
	bc %t24, label .L27, label .L25
.L25:
	%t26 = call i32 @func(i32 %l1)
	bc %t26, label .L27, label .L28
.L27:
	%l1 = 1
	br label .L29
.L28:
	%l1 = 0
	br label .L29
.L29:
	%t30 = call i32 @func(i32 99)
	bc %t30, label .L34, label .L31
.L31:
	%t32 = call i32 @func(i32 100)
	bc %t32, label .L33, label .L34
.L33:
	%l1 = 1
	br label .L35
.L34:
	%l1 = 0
	br label .L35
.L35:
	%l0 = 0
	br label .L36
.L36:
	exit %l0
}
