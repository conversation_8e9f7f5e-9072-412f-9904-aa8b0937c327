// 实验八：数组支持和内置函数测试文件

int main() {
    // 一维数组定义和初始化
    int a[10];
    
    // 使用getarray读取数组元素
    int n = getarray(a);
    
    // 数组元素访问和修改
    a[0] = 1;
    a[1] = 2;
    a[2] = 3;
    
    // 使用putarray输出数组
    putarray(n, a);
    
    // 二维数组定义
    int b[3][4];
    
    // 二维数组元素访问
    b[0][0] = 10;
    b[1][2] = 20;
    b[2][3] = 30;
    
    // 读取二维数组元素
    int x = b[0][0];
    int y = b[1][2];
    int z = b[2][3];
    
    // 输出结果
    putint(x);
    putint(y);
    putint(z);
    
    return 0;
}

// 数组作为函数参数的测试
int arraySum(int arr[], int size) {
    int sum = 0;
    int i = 0;
    while (i < size) {
        sum = sum + arr[i];
        i = i + 1;
    }
    return sum;
}

// 二维数组作为函数参数的测试
int matrixSum(int matrix[][4], int rows) {
    int sum = 0;
    int i = 0;
    int j = 0;
    
    while (i < rows) {
        j = 0;
        while (j < 4) {
            sum = sum + matrix[i][j];
            j = j + 1;
        }
        i = i + 1;
    }
    return sum;
}

// 测试函数调用
int testArrayFunctions() {
    int arr[5];
    arr[0] = 1;
    arr[1] = 2;
    arr[2] = 3;
    arr[3] = 4;
    arr[4] = 5;
    
    int sum1 = arraySum(arr, 5);
    
    int matrix[2][4];
    matrix[0][0] = 1; matrix[0][1] = 2; matrix[0][2] = 3; matrix[0][3] = 4;
    matrix[1][0] = 5; matrix[1][1] = 6; matrix[1][2] = 7; matrix[1][3] = 8;
    
    int sum2 = matrixSum(matrix, 2);
    
    return sum1 + sum2;
}
