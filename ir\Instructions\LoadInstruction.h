#pragma once

#include "Instruction.h"
#include "Value.h"
#include "Function.h"

class LoadInstruction : public Instruction {
public:
    LoadInstruction(Function* _func, Value* result, Value* addr)
        : Instruction(_func, IRInstOperator::IRINST_OP_LOAD, result->getType()) {
        addOperand(result);
        addOperand(addr);
    }

    void toString(std::string& str) override {
        Value* result = getOperand(0);
        Value* addr = getOperand(1);
        str = result->getIRName() + " = load " + getType()->toString() + ", " + addr->getType()->toString() + "* " + addr->getIRName();
    }
}; 