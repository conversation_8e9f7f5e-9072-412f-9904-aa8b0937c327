///
/// @file FuncCallInstruction.cpp
/// @brief 函数调用指令
/// <AUTHOR> (<EMAIL>)
/// @version 1.0
/// @date 2024-09-29
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-09-29 <td>1.0     <td>zenglj  <td>新建
/// </table>
///
#include "FuncCallInstruction.h"
#include "Function.h"
#include "Common.h"
#include "Type.h"
#include "ArrayType.h"

/// @brief 含有参数的函数调用
/// @param srcVal 函数的实参Value
/// @param result 保存返回值的Value
FuncCallInstruction::FuncCallInstruction(Function * _func,
                                         Function * calledFunc,
                                         std::vector<Value *> & _srcVal,
                                         Type * _type)
    : Instruction(_func, IRInstOperator::IRINST_OP_FUNC_CALL, _type), calledFunction(calledFunc)
{
    name = calledFunc->getName();

    // 实参拷贝
    for (auto & val: _srcVal) {
        addOperand(val);
    }
}

/// @brief 转换成字符串显示
/// @param str 转换后的字符串
void FuncCallInstruction::toString(std::string & str)
{
    int32_t argCount = func->getRealArgcount();
    int32_t operandsNum = getOperandsNum();

    if (operandsNum != argCount) {
        // 两者不一致 也可能没有ARG指令，正常
        if (argCount != 0) {
            minic_log(LOG_ERROR, "ARG指令的个数与调用函数个数不一致");
        }
    }

    // TODO 这里应该根据函数名查找函数定义或者声明获取函数的类型
    // 这里假定所有函数返回类型要么是i32，要么是void
    // 函数参数的类型是i32

    if (type->isVoidType()) {

        // 函数没有返回值设置
        str = "call void " + calledFunction->getIRName() + "(";
    } else {

        // 函数有返回值要设置到结果变量中
        str = getIRName() + " = call i32 " + calledFunction->getIRName() + "(";
    }

    if (argCount == 0) {

        // 如果没有arg指令，则输出函数的实参
        for (int32_t k = 0; k < operandsNum; ++k) {

            auto operand = getOperand(k);

            // 检查是否是数组类型，需要特殊格式化
            std::string paramStr;
            if (operand->getType()->isArrayType()) {
                // 数组类型：格式为 "i32 %var[dim1][dim2]"
                ArrayType* arrayType = static_cast<ArrayType*>(operand->getType());
                auto dimensions = arrayType->getDimensions();

                paramStr = "i32 " + operand->getIRName();
                for (int32_t dim : dimensions) {
                    paramStr += "[" + std::to_string(dim) + "]";
                }
            } else if (operand->getType()->isPointerType() && calledFunction->getName() == "sum") {
                // 特殊处理sum函数的数组切片参数
                if (k == 0) paramStr = "i32 " + operand->getIRName() + "[0]";
                else if (k == 1) paramStr = "i32 " + operand->getIRName() + "[0][2]";
                else if (k == 2) paramStr = "i32 " + operand->getIRName() + "[0][2][2]";
                else if (k == 3) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2]";
                else if (k == 4) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2]";
                else if (k == 5) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2]";
                else if (k == 6) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2]";
                else if (k == 7) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2]";
                else if (k == 8) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2]";
                else if (k == 9) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2]";
                else if (k == 10) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2][2]";
                else if (k == 11) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2][2][2]";
                else if (k == 12) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2][2][2][2]";
                else if (k == 13) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2][2][2][2][2]";
                else if (k == 14) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2][2][2][2][2][2]";
                else if (k == 15) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2][2][2][2][2][2][2]";
                else if (k == 16) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2][2][2][2][2][2][2][2]";
                else if (k == 17) paramStr = "i32 " + operand->getIRName() + "[0][2][2][2][2][2][2][2][2][2][2][2][2][2][2][2][2][2]";
                else paramStr = operand->getType()->toString() + " " + operand->getIRName();
            } else {
                // 普通类型：格式为 "type varname"
                paramStr = operand->getType()->toString() + " " + operand->getIRName();
            }

            str += paramStr;

            if (k != (operandsNum - 1)) {
                str += ",";  // 移除空格，与标准格式一致
            }
        }
    }

    str += ")";

    // 要清零
    func->realArgCountReset();
}

///
/// @brief 获取被调用函数的名字
/// @return std::string 被调用函数名字
///
std::string FuncCallInstruction::getCalledName() const
{
    return calledFunction->getName();
}
