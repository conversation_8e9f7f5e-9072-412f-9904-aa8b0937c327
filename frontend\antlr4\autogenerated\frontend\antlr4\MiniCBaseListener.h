
// Generated from frontend/antlr4/MiniC.g4 by ANTLR 4.13.1

#pragma once


#include "antlr4-runtime.h"
#include "MiniCListener.h"


/**
 * This class provides an empty implementation of MiniCListener,
 * which can be extended to create a listener which only needs to handle a subset
 * of the available methods.
 */
class  MiniCBaseListener : public MiniCListener {
public:

  virtual void enterCompileUnit(MiniCParser::CompileUnitContext * /*ctx*/) override { }
  virtual void exitCompileUnit(MiniCParser::CompileUnitContext * /*ctx*/) override { }

  virtual void enterFuncDef(MiniCParser::FuncDefContext * /*ctx*/) override { }
  virtual void exitFuncDef(MiniCParser::FuncDefContext * /*ctx*/) override { }

  virtual void enterBlock(MiniCParser::BlockContext * /*ctx*/) override { }
  virtual void exitBlock(MiniCParser::BlockContext * /*ctx*/) override { }

  virtual void enterBlockItemList(MiniCParser::BlockItemListContext * /*ctx*/) override { }
  virtual void exitBlockItemList(MiniCParser::BlockItemListContext * /*ctx*/) override { }

  virtual void enterBlockItem(MiniCParser::BlockItemContext * /*ctx*/) override { }
  virtual void exitBlockItem(MiniCParser::BlockItemContext * /*ctx*/) override { }

  virtual void enterVarDecl(MiniCParser::VarDeclContext * /*ctx*/) override { }
  virtual void exitVarDecl(MiniCParser::VarDeclContext * /*ctx*/) override { }

  virtual void enterFuncParamList(MiniCParser::FuncParamListContext * /*ctx*/) override { }
  virtual void exitFuncParamList(MiniCParser::FuncParamListContext * /*ctx*/) override { }

  virtual void enterFuncParam(MiniCParser::FuncParamContext * /*ctx*/) override { }
  virtual void exitFuncParam(MiniCParser::FuncParamContext * /*ctx*/) override { }

  virtual void enterBasicType(MiniCParser::BasicTypeContext * /*ctx*/) override { }
  virtual void exitBasicType(MiniCParser::BasicTypeContext * /*ctx*/) override { }

  virtual void enterVarDef(MiniCParser::VarDefContext * /*ctx*/) override { }
  virtual void exitVarDef(MiniCParser::VarDefContext * /*ctx*/) override { }

  virtual void enterReturnStatement(MiniCParser::ReturnStatementContext * /*ctx*/) override { }
  virtual void exitReturnStatement(MiniCParser::ReturnStatementContext * /*ctx*/) override { }

  virtual void enterAssignStatement(MiniCParser::AssignStatementContext * /*ctx*/) override { }
  virtual void exitAssignStatement(MiniCParser::AssignStatementContext * /*ctx*/) override { }

  virtual void enterIfStatement(MiniCParser::IfStatementContext * /*ctx*/) override { }
  virtual void exitIfStatement(MiniCParser::IfStatementContext * /*ctx*/) override { }

  virtual void enterWhileStatement(MiniCParser::WhileStatementContext * /*ctx*/) override { }
  virtual void exitWhileStatement(MiniCParser::WhileStatementContext * /*ctx*/) override { }

  virtual void enterBlockStatement(MiniCParser::BlockStatementContext * /*ctx*/) override { }
  virtual void exitBlockStatement(MiniCParser::BlockStatementContext * /*ctx*/) override { }

  virtual void enterExpressionStatement(MiniCParser::ExpressionStatementContext * /*ctx*/) override { }
  virtual void exitExpressionStatement(MiniCParser::ExpressionStatementContext * /*ctx*/) override { }

  virtual void enterBreakStatement(MiniCParser::BreakStatementContext * /*ctx*/) override { }
  virtual void exitBreakStatement(MiniCParser::BreakStatementContext * /*ctx*/) override { }

  virtual void enterContinueStatement(MiniCParser::ContinueStatementContext * /*ctx*/) override { }
  virtual void exitContinueStatement(MiniCParser::ContinueStatementContext * /*ctx*/) override { }

  virtual void enterExpr(MiniCParser::ExprContext * /*ctx*/) override { }
  virtual void exitExpr(MiniCParser::ExprContext * /*ctx*/) override { }

  virtual void enterOrExp(MiniCParser::OrExpContext * /*ctx*/) override { }
  virtual void exitOrExp(MiniCParser::OrExpContext * /*ctx*/) override { }

  virtual void enterAndExp(MiniCParser::AndExpContext * /*ctx*/) override { }
  virtual void exitAndExp(MiniCParser::AndExpContext * /*ctx*/) override { }

  virtual void enterEqlExp(MiniCParser::EqlExpContext * /*ctx*/) override { }
  virtual void exitEqlExp(MiniCParser::EqlExpContext * /*ctx*/) override { }

  virtual void enterRelExp(MiniCParser::RelExpContext * /*ctx*/) override { }
  virtual void exitRelExp(MiniCParser::RelExpContext * /*ctx*/) override { }

  virtual void enterAddExp(MiniCParser::AddExpContext * /*ctx*/) override { }
  virtual void exitAddExp(MiniCParser::AddExpContext * /*ctx*/) override { }

  virtual void enterMulExp(MiniCParser::MulExpContext * /*ctx*/) override { }
  virtual void exitMulExp(MiniCParser::MulExpContext * /*ctx*/) override { }

  virtual void enterAddOp(MiniCParser::AddOpContext * /*ctx*/) override { }
  virtual void exitAddOp(MiniCParser::AddOpContext * /*ctx*/) override { }

  virtual void enterMulOp(MiniCParser::MulOpContext * /*ctx*/) override { }
  virtual void exitMulOp(MiniCParser::MulOpContext * /*ctx*/) override { }

  virtual void enterEqlOp(MiniCParser::EqlOpContext * /*ctx*/) override { }
  virtual void exitEqlOp(MiniCParser::EqlOpContext * /*ctx*/) override { }

  virtual void enterRelOp(MiniCParser::RelOpContext * /*ctx*/) override { }
  virtual void exitRelOp(MiniCParser::RelOpContext * /*ctx*/) override { }

  virtual void enterUnaryOp(MiniCParser::UnaryOpContext * /*ctx*/) override { }
  virtual void exitUnaryOp(MiniCParser::UnaryOpContext * /*ctx*/) override { }

  virtual void enterUnaryExp(MiniCParser::UnaryExpContext * /*ctx*/) override { }
  virtual void exitUnaryExp(MiniCParser::UnaryExpContext * /*ctx*/) override { }

  virtual void enterPrimaryExp(MiniCParser::PrimaryExpContext * /*ctx*/) override { }
  virtual void exitPrimaryExp(MiniCParser::PrimaryExpContext * /*ctx*/) override { }

  virtual void enterRealParamList(MiniCParser::RealParamListContext * /*ctx*/) override { }
  virtual void exitRealParamList(MiniCParser::RealParamListContext * /*ctx*/) override { }

  virtual void enterIntLiteral(MiniCParser::IntLiteralContext * /*ctx*/) override { }
  virtual void exitIntLiteral(MiniCParser::IntLiteralContext * /*ctx*/) override { }

  virtual void enterLVal(MiniCParser::LValContext * /*ctx*/) override { }
  virtual void exitLVal(MiniCParser::LValContext * /*ctx*/) override { }


  virtual void enterEveryRule(antlr4::ParserRuleContext * /*ctx*/) override { }
  virtual void exitEveryRule(antlr4::ParserRuleContext * /*ctx*/) override { }
  virtual void visitTerminal(antlr4::tree::TerminalNode * /*node*/) override { }
  virtual void visitErrorNode(antlr4::tree::ErrorNode * /*node*/) override { }

};

