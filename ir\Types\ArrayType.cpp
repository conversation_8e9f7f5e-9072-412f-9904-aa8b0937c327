///
/// @file ArrayType.cpp
/// @brief 数组类型描述类，实验八新增
///
/// <AUTHOR> (<EMAIL>)
/// @version 1.0
/// @date 2024-11-23
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-11-23 <td>1.0     <td>zenglj  <td>新建
/// </table>
///

#include "ArrayType.h"
#include <unordered_map>
#include <string>

// 用于缓存已创建的数组类型，避免重复创建
static std::unordered_map<std::string, ArrayType*> arrayTypeCache;

///
/// @brief 生成数组类型的唯一键值
/// @param elementType 元素类型
/// @param dimensions 维度信息
/// @return std::string
///
static std::string generateArrayTypeKey(Type * elementType, const std::vector<int32_t> & dimensions)
{
    std::string key = elementType->toString();
    for (int32_t dim : dimensions) {
        key += "[" + std::to_string(dim) + "]";
    }
    return key;
}

///
/// @brief 获取数组类型
/// @param elementType 数组元素类型
/// @param dimensions 数组各维度大小
/// @return ArrayType*
///
ArrayType * ArrayType::getType(Type * elementType, const std::vector<int32_t> & dimensions)
{
    // 生成唯一键值
    std::string key = generateArrayTypeKey(elementType, dimensions);
    
    // 查找缓存
    auto it = arrayTypeCache.find(key);
    if (it != arrayTypeCache.end()) {
        return it->second;
    }
    
    // 创建新的数组类型
    ArrayType * newArrayType = new ArrayType(elementType, dimensions);
    arrayTypeCache[key] = newArrayType;
    
    return newArrayType;
}
