// 实验八：Dragon IR数组规范测试文件
// 测试是否按照Dragon IR规范生成正确的数组访问指令

int main() {
    // 测试一维数组访问
    int a[10];
    int k = 5;
    int m;
    
    // 一维数组元素取值：m = a[k]
    // 应该生成：
    // %t10 = mul %l5, 4
    // %t11 = add %l2, %t10
    // %l6 = *%t11
    m = a[k];
    
    // 一维数组元素设值：a[k] = m
    // 应该生成：
    // %t10 = mul %l5, 4
    // %t11 = add %l2, %t10
    // *%t11 = %l6
    a[k] = m;
    
    // 测试二维数组访问
    int b[10][10];
    int i = 2;
    int j = 3;
    int t;
    
    // 二维数组元素取值：t = b[i][j]
    // 应该生成：
    // %t5 = mul %l2, 10
    // %t6 = add %t5, %l3
    // %t7 = mul %t6, 4
    // %t8 = add %l1, %t7
    // %l4 = *%t8
    t = b[i][j];
    
    // 二维数组元素设值：b[i][j] = t
    // 应该生成：
    // %t5 = mul %l2, 10
    // %t6 = add %t5, %l3
    // %t7 = mul %t6, 4
    // %t8 = add %l1, %t7
    // *%t8 = %l4
    b[i][j] = t;
    
    return 0;
}

// 测试数组作为函数参数
int testArrayParam(int arr[], int matrix[][5]) {
    // 形参数组应该生成：
    // define i32 @testArrayParam(i32* %t0, i32* %t1) {
    // declare i32* %l2  ; arr对应的局部变量
    // declare i32* %l3  ; matrix对应的局部变量
    // entry
    // %l2 = %t0
    // %l3 = %t1
    
    int result = arr[0] + matrix[0][0];
    return result;
}
