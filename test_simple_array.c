// 实验八：简单数组测试文件

int main() {
    // 一维数组定义
    int a[5];
    
    // 数组元素赋值
    a[0] = 10;
    a[1] = 20;
    a[2] = 30;
    
    // 数组元素读取
    int x = a[0];
    int y = a[1];
    int z = a[2];
    
    // 输出结果
    putint(x);
    putint(y);
    putint(z);
    
    // 二维数组定义
    int b[2][3];
    
    // 二维数组元素赋值
    b[0][0] = 1;
    b[0][1] = 2;
    b[1][0] = 3;
    b[1][1] = 4;
    
    // 二维数组元素读取
    int p = b[0][0];
    int q = b[1][1];
    
    putint(p);
    putint(q);
    
    return 0;
}
