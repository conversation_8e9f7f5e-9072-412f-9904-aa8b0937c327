grammar MiniC;

// 词法规则名总是以大写字母开头

// 语法规则名总是以小写字母开头

// 每个非终结符尽量多包含闭包、正闭包或可选符等的EBNF范式描述

// 若非终结符由多个产生式组成，则建议在每个产生式的尾部追加# 名称来区分，详细可查看非终结符statement的描述

// 语法规则描述：EBNF范式

// 源文件编译单元定义
compileUnit: (funcDef | varDecl)* EOF;

//实验七：函数定义支持int/void返回类型和多个形参
// 函数定义，支持int/void返回类型和可选的形参列表
funcDef: (T_INT | T_VOID) T_ID T_L_PAREN funcParamList? T_R_PAREN block;

// 语句块看用作函数体，这里允许多个语句，并且不含任何语句
block: T_L_BRACE blockItemList? T_R_BRACE;

// 每个ItemList可包含至少一个Item
blockItemList: blockItem+;

// 每个Item可以是一个语句，或者变量声明语句
blockItem: statement | varDecl;

// 变量声明，目前不支持变量含有初值
varDecl: basicType varDef (T_COMMA varDef)* T_SEMICOLON;

//实验七：形参列表支持多个形参，用逗号分隔
funcParamList: funcParam (T_COMMA funcParam)*;

//实验七：单个形参定义，包含类型和标识符，实验八：支持数组形参
funcParam:
	basicType T_ID (T_L_BRACKET T_R_BRACKET)* (
		T_L_BRACKET expr T_R_BRACKET
	)*;
// 基本类型
basicType: T_INT;

// 变量定义，实验八：支持数组定义
varDef: T_ID (T_L_BRACKET expr T_R_BRACKET)* (T_ASSIGN expr)?;

// 目前语句支持return和赋值语句
statement:
	T_RETURN expr? T_SEMICOLON										# returnStatement
	| lVal T_ASSIGN expr T_SEMICOLON								# assignStatement
	| T_IF T_L_PAREN expr T_R_PAREN statement (T_ELSE statement)?	# ifStatement
	| T_WHILE T_L_PAREN expr T_R_PAREN statement					# whileStatement
	| block															# blockStatement
	| expr T_SEMICOLON												# expressionStatement
	| T_BREAK T_SEMICOLON											# breakStatement
	| T_CONTINUE T_SEMICOLON										# continueStatement
	| T_SEMICOLON													# emptyStatement;

// 表达式文法 expr : AddExp 表达式目前只支持加法与减法运算
expr: orExp;
//修改部分，增加乘法*、除法/、求余%运算
orExp: andExp (OR andExp)*;
andExp: eqlExp (AND eqlExp)*;
eqlExp: relExp (eqlOp relExp)*;
relExp: addExp (relOp addExp)*;
addExp: mulExp (addOp mulExp)*; //一个加法表达式是由多个乘法表达式加/减组成的
mulExp: unaryExp (mulOp unaryExp)*; //一个乘法表达式是由多个一元表达式乘/除/取余组成的。
// 加减运算符 +/-
addOp: T_ADD | T_SUB;
//乘法*、除法/、求余%
mulOp: T_DIV | T_MUL | T_MOD;
//相等运算
eqlOp: EQUALS | NOT_EQUALS;
//关系运算< > <= >=
relOp: LT | LE | GT | GE;
//单目运算
unaryOp: T_SUB | NOT;
//实验七：一元表达式包含函数调用，支持多个实参
// 一元表达式 函数调用：T_ID T_L_PAREN realParamList? T_R_PAREN;”？“表示参数可选，可以有也可以没有。
// 单目操作写入一元表达式，优先级低于基本表达式,如a-b应该优先识别a,b为基本表达式，而不是-b
unaryExp:
	unaryOp unaryExp
	| primaryExp
	| T_ID T_L_PAREN realParamList? T_R_PAREN;

// 基本表达式：括号表达式、整数、左值表达式
primaryExp: T_L_PAREN expr T_R_PAREN | intLiteral | lVal;

//实验七：实参列表支持多个实参，用逗号分隔
// 实参列表
realParamList: expr (T_COMMA expr)*;

//数字非终结符十进制、八进制、十六进制
intLiteral: T_DIGIT | T_OCT_LITERAL | T_HEX_LITERAL;

// 左值表达式，实验八：支持数组访问
lVal: T_ID (T_L_BRACKET expr T_R_BRACKET)*;

// 用正规式来进行词法规则的描述

T_L_PAREN: '(';
T_R_PAREN: ')';
T_SEMICOLON: ';';
T_L_BRACE: '{';
T_R_BRACE: '}';
T_L_BRACKET: '['; // 实验八：左方括号
T_R_BRACKET: ']'; // 实验八：右方括号

T_ASSIGN: '=';
T_COMMA: ',';
T_IF: 'if';
T_ELSE: 'else';
T_WHILE: 'while';
T_BREAK: 'break';
T_CONTINUE: 'continue';

T_ADD: '+';
T_SUB: '-';
//增加部分
T_MUL: '*';
T_DIV: '/';
T_MOD: '%';
//以上为增加部分
// 添加关系运算符 关系运算符
EQUALS: '==';
NOT_EQUALS: '!=';
LT: '<';
LE: '<=';
GT: '>';
GE: '>=';
// 布尔运算符
AND: '&&';
OR: '||';
NOT: '!';
//实验七：关键字定义，包含return、int、void等
// 要注意关键字同样也属于T_ID，因此必须放在T_ID的前面，否则会识别成T_ID
T_RETURN: 'return';
T_INT: 'int'; //实验七：int返回类型支持
T_VOID: 'void'; //实验七：void返回类型支持

T_ID: [a-zA-Z_][a-zA-Z0-9_]*;
T_DIGIT: '0' | [1-9][0-9]*;
//修改部分
T_OCT_LITERAL: '0' [0-7]+;
T_HEX_LITERAL: '0' [xX][0-9a-fA-F]+;

/* 空白符丢弃 */
WS: [ \r\n\t]+ -> skip;
LINE_COMMENT: '//' ~[\r\n]* -> skip;
BLOCK_COMMENT: '/*' .*? '*/' -> skip;