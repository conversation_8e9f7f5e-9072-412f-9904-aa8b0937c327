// 第二步：验证数组作为函数参数

// 测试1：一维数组参数
int func1(int arr[10]) {
    arr[0] = 1;
    return arr[0];
}

// 测试2：多维数组参数（第一维可以为空）
int func2(int matrix[][5]) {
    matrix[0][0] = 2;
    return matrix[0][0];
}

// 测试3：多维数组参数（指定所有维度）
int func3(int cube[2][3][4]) {
    cube[0][0][0] = 3;
    return cube[0][0][0];
}

int main() {
    // 声明数组
    int a[10];
    int b[3][5];
    int c[2][3][4];
    
    // 调用函数
    int x = func1(a);
    int y = func2(b);
    int z = func3(c);
    
    return x + y + z;
}
