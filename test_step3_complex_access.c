// 第三步：验证复杂数组访问

int main() {
    // 声明各种数组
    int a[10];
    int b[5][3];
    int c[2][3][4];
    
    // 测试1：使用变量作为索引
    int i = 1;
    int j = 2;
    int k = 3;
    
    a[i] = 10;
    b[i][j] = 20;
    c[0][i][j] = 30;
    
    // 测试2：使用表达式作为索引
    a[i + 1] = 40;
    b[i - 1][j + 1] = 50;
    c[1][i * 2][j - 1] = 60;
    
    // 测试3：嵌套数组访问
    int x = a[b[1][0]];  // 使用数组元素作为索引
    
    // 测试4：数组元素的运算
    int result = a[0] + b[1][2] + c[0][1][2];
    
    return result;
}
