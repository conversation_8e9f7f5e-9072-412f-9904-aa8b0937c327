#!/bin/bash

# 实验八数组支持快速验证脚本

echo "🧪 开始验证实验八数组支持功能..."

cd build

echo ""
echo "📋 第一步：验证基础数组语法解析"
echo "命令: ./minic -S -I -A ../test_step1_basic_syntax.c -o test1.ir"
./minic -S -I -A ../test_step1_basic_syntax.c -o test1.ir
if [ $? -eq 0 ]; then
    echo "✅ 基础数组语法解析成功"
    echo "📄 生成的IR文件: test1.ir"
    echo "🔍 检查数组声明..."
    grep -n "declare.*\[" test1.ir || echo "⚠️  未找到数组声明"
else
    echo "❌ 基础数组语法解析失败"
fi

echo ""
echo "📋 第二步：验证函数参数数组"
echo "命令: ./minic -S -I -A ../test_step2_function_params.c -o test2.ir"
./minic -S -I -A ../test_step2_function_params.c -o test2.ir
if [ $? -eq 0 ]; then
    echo "✅ 函数参数数组解析成功"
    echo "📄 生成的IR文件: test2.ir"
else
    echo "❌ 函数参数数组解析失败"
fi

echo ""
echo "📋 第三步：验证复杂数组访问"
echo "命令: ./minic -S -I -A ../test_step3_complex_access.c -o test3.ir"
./minic -S -I -A ../test_step3_complex_access.c -o test3.ir
if [ $? -eq 0 ]; then
    echo "✅ 复杂数组访问解析成功"
    echo "📄 生成的IR文件: test3.ir"
else
    echo "❌ 复杂数组访问解析失败"
fi

echo ""
echo "📋 第四步：验证IR生成和降维处理（重点）"
echo "命令: ./minic -S -I -A ../test_step4_ir_generation.c -o test4.ir"
./minic -S -I -A ../test_step4_ir_generation.c -o test4.ir
if [ $? -eq 0 ]; then
    echo "✅ IR生成成功"
    echo "📄 生成的IR文件: test4.ir"
    echo ""
    echo "🔍 检查关键IR指令..."
    echo "数组声明:"
    grep -n "declare.*\[" test4.ir || echo "⚠️  未找到数组声明"
    echo ""
    echo "乘法指令 (降维计算):"
    grep -n "mul" test4.ir || echo "⚠️  未找到乘法指令"
    echo ""
    echo "加法指令 (地址计算):"
    grep -n "add" test4.ir || echo "⚠️  未找到加法指令"
    echo ""
    echo "内存访问指令:"
    grep -n "\*" test4.ir || echo "⚠️  未找到内存访问指令"
else
    echo "❌ IR生成失败"
fi

echo ""
echo "📋 第五步：验证AST生成"
echo "命令: ./minic -S -T -A ../test_step1_basic_syntax.c -o test1_ast.png"
./minic -S -T -A ../test_step1_basic_syntax.c -o test1_ast.png
if [ $? -eq 0 ]; then
    echo "✅ AST生成成功"
    echo "📄 生成的AST文件: test1_ast.png"
else
    echo "❌ AST生成失败"
fi

echo ""
echo "📋 第六步：验证汇编生成"
echo "命令: ./minic -S -A ../test_step4_ir_generation.c -o test4.s"
./minic -S -A ../test_step4_ir_generation.c -o test4.s
if [ $? -eq 0 ]; then
    echo "✅ 汇编生成成功"
    echo "📄 生成的汇编文件: test4.s"
else
    echo "❌ 汇编生成失败"
fi

echo ""
echo "🎯 验证总结:"
echo "如果以上步骤都显示 ✅，说明实验八的核心功能已经成功实现！"
echo ""
echo "📁 生成的文件:"
ls -la test*.ir test*.png test*.s 2>/dev/null || echo "请检查生成的文件"

echo ""
echo "🔍 详细验证建议:"
echo "1. 查看 test4.ir 文件，检查是否包含正确的Dragon IR指令"
echo "2. 查看 test1_ast.png 文件，检查AST中是否有数组相关节点"
echo "3. 如果所有文件都正常生成，说明实验八基本完成"
