# 实验八：数组支持实现记录

## 实验目标

在Minic编译器实现的基础上，支持一维数组和多维数组。主要功能要求：

1. 支持int类型的一维数组和多维数组的定义（局部变量、全局变量、形参变量）
2. 支持数组下标变量的访问（读和写）
3. 支持std.c中有关数组函数内置，如getarray、putarray函数
4. 在AST翻译成线性IR时，实现数组的降维处理
5. ARM32后端上支持数组的读写访问（可选）

## Dragon IR数组语法规范

### 数组定义规则
- **类型转换**：`int` → `i32`
- **局部数组变量**：使用 `%l` 开头的标识符
- **全局数组变量**：维度必须大于0
- **形参数组**：第一维设为0，其他维大于0
  - 例如：`int a[6][5]` → `i32 %t0[0][5]` 和 `declare i32 %l1[0][5]`

### 数组访问模式
- **一维数组访问**：`%t10 = mul %l5, 4; %t11 = add %l2, %t10; %l6 = *%t11`
- **多维数组降维**：通过行优先存储转换为一维偏移
- **字节偏移计算**：最终偏移需要乘以元素大小（int = 4字节）

## 实现过程

### 第一阶段：前端语法支持（已完成）

#### 1. 语法文件修改 (MiniC.g4)

**添加词法规则**：
```antlr
T_L_BRACKET: '[';  // 左方括号
T_R_BRACKET: ']';  // 右方括号
```

**修改语法规则**：
```antlr
// 变量定义支持数组
varDef: T_ID (T_L_BRACKET expr T_R_BRACKET)* (T_ASSIGN expr)?;

// 函数形参支持数组
funcParam: basicType T_ID (T_L_BRACKET T_R_BRACKET)* (T_L_BRACKET expr T_R_BRACKET)*;

// 左值表达式支持数组访问
lVal: T_ID (T_L_BRACKET expr T_R_BRACKET)*;
```

#### 2. AST节点类型扩展 (AST.h)

**新增节点类型**：
```cpp
/// @brief 数组访问运算符，实验八新增
AST_OP_ARRAY_ACCESS,
```

**新增节点创建函数**：
```cpp
///
/// @brief 创建数组访问节点，实验八新增
/// @param array_node 数组变量节点
/// @param index_node 索引表达式节点
/// @return ast_node* 数组访问节点
///
ast_node * create_array_access_node(ast_node * array_node, ast_node * index_node);
```

#### 3. AST节点创建实现 (AST.cpp)

**实现数组访问节点创建**：
```cpp
ast_node * create_array_access_node(ast_node * array_node, ast_node * index_node)
{
    // 创建数组访问节点
    ast_node * access_node = create_contain_node(ast_operator_type::AST_OP_ARRAY_ACCESS, array_node, index_node);
    
    // 数组访问的类型应该是数组元素的类型，这里暂时设为int类型
    // 在后续的语义分析阶段会进行更精确的类型推导
    access_node->type = IntegerType::getTypeInt();
    
    return access_node;
}
```

#### 4. CST到AST转换器修改 (Antlr4CSTVisitor.h/.cpp)

**修改visitLVal函数**：
```cpp
std::any MiniCCSTVisitor::visitLVal(MiniCParser::LValContext * ctx)
{
    // 获取ID的名字和行号
    auto varId = ctx->T_ID()->getText();
    int64_t lineNo = (int64_t) ctx->T_ID()->getSymbol()->getLine();
    
    // 创建基础变量节点
    ast_node * base_node = ast_node::New(varId, lineNo);

    // 如果没有数组索引，直接返回变量节点
    if (ctx->expr().empty()) {
        return base_node;
    }

    // 处理数组访问：收集所有索引表达式
    std::vector<ast_node *> indices;
    for (auto exprCtx : ctx->expr()) {
        indices.push_back(std::any_cast<ast_node *>(visitExpr(exprCtx)));
    }

    // 使用辅助函数处理多维数组访问
    return processArrayAccess(base_node, indices);
}
```

**新增辅助函数**：
```cpp
ast_node * MiniCCSTVisitor::processArrayAccess(ast_node * base_node, const std::vector<ast_node *> & indices)
{
    ast_node * current = base_node;
    
    // 逐层构建数组访问节点
    for (ast_node * index : indices) {
        current = create_array_access_node(current, index);
    }
    
    return current;
}
```

**修改visitVarDef函数**：
```cpp
std::any MiniCCSTVisitor::visitVarDef(MiniCParser::VarDefContext * ctx)
{
    auto varId = ctx->T_ID()->getText();
    int64_t lineNo = (int64_t) ctx->T_ID()->getSymbol()->getLine();
    ast_node * id_node = ast_node::New(varId, lineNo);

    // 如果有数组维度，需要将维度信息附加到节点上
    if (!ctx->expr().empty()) {
        // 这是数组定义，创建一个包含维度信息的节点
        ast_node * array_def_node = create_contain_node(ast_operator_type::AST_OP_VAR_DECL);
        array_def_node->name = varId;
        array_def_node->line_no = lineNo;
        
        // 添加变量名作为第一个子节点
        array_def_node->insert_son_node(id_node);
        
        // 添加所有维度表达式作为子节点
        for (auto exprCtx : ctx->expr()) {
            ast_node * dim_node = std::any_cast<ast_node *>(visitExpr(exprCtx));
            array_def_node->insert_son_node(dim_node);
        }
        
        return array_def_node;
    }

    return id_node;
}
```

**修改visitFuncParam函数**：
```cpp
std::any MiniCCSTVisitor::visitFuncParam(MiniCParser::FuncParamContext *ctx)
{
    // 处理基本类型和参数名
    type_attr paramType{BasicType::TYPE_INT, (int64_t)ctx->basicType()->start->getLine()};
    char *id = strdup(ctx->T_ID()->getText().c_str());
    
    ast_node *paramNode = new ast_node(ast_operator_type::AST_OP_FUNC_FORMAL_PARAM);
    paramNode->name = std::string(id);
    paramNode->type = typeAttr2Type(paramType);

    // 处理数组形参的维度信息
    for (auto exprCtx : ctx->expr()) {
        ast_node * dim_node = std::any_cast<ast_node *>(visitExpr(exprCtx));
        paramNode->insert_son_node(dim_node);
    }

    free(id);
    return paramNode;
}
```

### 支持的数组语法特性

#### ✅ 已实现的功能
1. **一维数组定义**：`int a[10];`
2. **多维数组定义**：`int b[5][3];`
3. **数组元素访问**：`a[i]`, `b[i][j]`
4. **数组元素赋值**：`a[0] = 1;`, `b[1][2] = 5;`
5. **数组作为函数形参**：`int func(int arr[10], int matrix[][5])`

#### 测试用例
```c
// 实验八：数组支持测试文件

int main() {
    // 一维数组定义
    int a[10];
    
    // 二维数组定义
    int b[5][3];
    
    // 数组元素访问
    a[0] = 1;
    b[1][2] = 5;
    
    // 数组元素读取
    int x = a[0];
    int y = b[1][2];
    
    return 0;
}

// 数组作为函数参数
int func(int arr[10], int matrix[][5]) {
    arr[0] = 1;
    matrix[0][0] = 2;
    return arr[0] + matrix[0][0];
}
```

### 第二阶段：IR生成（已完成基础实现）

#### ✅ 已实现的功能

1. **ArrayType类实现**
   - 创建了完整的数组类型系统
   - 支持多维数组类型描述
   - 区分局部数组和形参数组（第一维为0）
   - 实现了类型缓存机制

2. **数组声明的IR生成**
   - 修改了`ir_variable_declare`函数支持数组声明
   - 支持常量维度的数组定义
   - 创建ArrayType类型的变量

3. **数组访问的IR生成**
   - 实现了`ir_array_access`函数
   - 按照Dragon IR规范生成数组访问指令：
     ```
     %t10 = mul index, 4        // 计算字节偏移
     %t11 = add array_base, %t10 // 计算元素地址
     ```
   - 支持嵌套数组访问（多维数组）

4. **构建系统更新**
   - 在CMakeLists.txt中添加了ArrayType源文件
   - 更新了包含路径

#### 🔧 ArrayType类关键特性

```cpp
class ArrayType : public Type {
public:
    // 获取数组类型（带缓存）
    static ArrayType * getType(Type * elementType, const std::vector<int32_t> & dimensions);

    // 获取数组总元素个数
    int32_t getTotalElements() const;

    // 是否是形参数组（第一维为0）
    bool isParameterArray() const;

    // 获取类型大小（形参数组返回指针大小4字节）
    int32_t getSize() const override;

    // 生成Dragon IR类型字符串：i32[10][15]
    std::string toString() const override;
};
```

#### 🔧 数组访问IR生成示例

**输入代码**：
```c
int a[10];
a[5] = 42;
```

**生成的Dragon IR**：
```
declare i32 %l1[10]           // 数组声明
%t10 = mul 5, 4               // 索引×元素大小
%t11 = add %l1, %t10          // 数组基址+偏移
*%t11 = 42                    // 存储值
```

## 下一步工作

### 第三阶段：多维数组降维处理（待实现）
1. **多维数组访问优化**
   - 实现完整的多维数组降维算法
   - 支持任意维度的数组访问
2. **形参数组特殊处理**
   - 实现形参数组第一维为0的处理
   - 支持函数参数中的数组传递

### 第三阶段：语义分析（待实现）
1. **数组类型检查**
2. **数组边界检查**（可选）
3. **数组维度匹配检查**

### 第四阶段：ARM32后端（可选）
1. **数组内存布局**
2. **数组访问指令生成**

## 技术要点

### 数组与指针的区别
- **局部变量**：`int A[2][3]` 需要分配 6个int 的空间
- **函数形参**：`int A[2][3]` 只需分配 4字节（指针大小）
- **Dragon IR形参**：`int A[2][3]` → `i32 %t0[0][3]`（第一维为0）

### 多维数组降维公式
- **二维数组**：`a[i][j]` → `a + (i*cols + j)*sizeof(element)`
- **三维数组**：`a[i][j][k]` → `a + ((i*dim2 + j)*dim3 + k)*sizeof(element)`

## 编译错误修复记录

1. **IntType未声明错误**：修改为 `IntegerType::getTypeInt()`
2. **vector转bool错误**：修改数组初始化表达式处理逻辑
3. **头文件包含问题**：确保正确包含相关类型定义
4. **ConstInt方法名错误**：修改 `getValue()` 为 `getVal()`

## 总结

前端antlr4的数组支持已经基本完成，包括：
- ✅ 语法规则扩展
- ✅ AST节点类型定义
- ✅ CST到AST转换逻辑
- ✅ 多维数组访问处理
- ✅ 数组形参支持

下一步需要实现IR生成阶段的数组降维处理和Dragon IR指令生成。
