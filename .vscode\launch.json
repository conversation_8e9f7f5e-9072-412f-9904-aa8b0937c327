{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"type": "antlr-debug",
			"request": "launch",
			// 调试的名称，可修改
			"name": "Debug minic Antlr4 Grammar",
			// 文法解析的输入文件
			"input": "tests/test1-1.c",
			// 分析树可视化，也就是具体语法树AST
			"visualParseTree": true,
			// 文本输出分析树
			"printParseTree": true,
			// 语法的开始符号
			"startRule": "compileUnit",
			// 描述语法的g4文件
			"grammar": "${workspaceFolder}/frontend/antlr4/MiniC.g4"
		},
		{
			// 可采用GCC或者Clang编译器
			"name": "minic Debug IR vscode-lldb",
			"type": "lldb",
			"request": "launch",
			"program": "${workspaceFolder}/build/minic",
			"args": ["-S", "-I", "-D", "-o", "tests/test1-1.ir", "tests/test1-1.c"],
			"cwd": "${workspaceFolder}",
			// 不需要编译时可注释掉
			"preLaunchTask": "CMake: build"
		},
		{
			// 可采用GCC或者Clang编译器
			"name": "minic Debug IR",
			"type": "cppdbg",
			"request": "launch",
			"program": "${workspaceFolder}/build/minic",
			"args": ["-S", "-D", "-I", "-o", "tests/test1-1.ir", "tests/test1-1.c"],
			// 程序的入口（main函数的入口）是否停止
			"stopAtEntry": true,
			"cwd": "${workspaceFolder}",
			"environment": [],
			"externalConsole": false,
			// 根据需要修改修改目标架构，这里假定amd64
			"targetArchitecture": "amd64",
			"linux": {
				"MIMode": "gdb",
				"miDebuggerPath": "/usr/bin/gdb"
			},
			"osx": {
				"MIMode": "lldb"
			},
			"windows": {
				"MIMode": "gdb"
			},
			"setupCommands": [
				{
					"description": "Enable pretty-printing for gdb",
					"text": "-enable-pretty-printing",
					"ignoreFailures": true
				},
				{
					"description": "Set Disassembly Flavor to Intel",
					"text": "-gdb-set disassembly-flavor intel",
					"ignoreFailures": true
				}
			],
			// 不需要编译时可注释掉
			"preLaunchTask": "CMake: build"
		},
		{
			// 可采用GCC或者Clang编译器
			"name": "minic Debug AST",
			"type": "cppdbg",
			"request": "launch",
			"program": "${workspaceFolder}/build/minic",
			"args": ["-S", "-A", "-T", "-o", "tests/test1-1.png", "tests/test1-1.c"],
			// 程序的入口（main函数的入口）是否停止
			"stopAtEntry": true,
			"cwd": "${workspaceFolder}",
			"environment": [],
			"externalConsole": false,
			// 根据需要修改修改目标架构，这里假定amd64
			// "targetArchitecture": "amd64",
			"linux": {
				"MIMode": "gdb",
				"miDebuggerPath": "/usr/bin/gdb"
			},
			"osx": {
				"MIMode": "lldb"
			},
			"windows": {
				"MIMode": "gdb"
			},
			"setupCommands": [
				{
					"description": "Enable pretty-printing for gdb",
					"text": "-enable-pretty-printing",
					"ignoreFailures": true
				},
				{
					"description": "Set Disassembly Flavor to Intel",
					"text": "-gdb-set disassembly-flavor intel",
					"ignoreFailures": true
				}
			],
			// 不需要编译时可注释掉
			"preLaunchTask": "CMake: build"
		},
		{
			// 可采用GCC或者Clang编译器
			"name": "minic Debug Backend ARM32",
			"type": "lldb",
			"request": "launch",
			"program": "${workspaceFolder}/build/minic",
			"args": ["-S", "-A", "-o", "tests/test1-1.s", "tests/test1-1.c"],
			"cwd": "${workspaceFolder}",
			// 不需要编译时可注释掉
			"preLaunchTask": "CMake: build"
		},
		{
			"name": "Qemu Debug minic arm32",
			"type": "cppdbg",
			"request": "launch",
			"program": "${workspaceFolder}/tests/test1-1",
			"args": [],
			// 程序的入口（main函数的入口）是否停止
			"stopAtEntry": true,
			"cwd": "${workspaceFolder}",
			"miDebuggerServerAddress": "localhost:1234",
			"miDebuggerPath": "/usr/bin/gdb-multiarch",
			"environment": [],
			"externalConsole": false,
			"MIMode": "gdb",
			"setupCommands": [
				{
					"description": "pretty printing",
					"text": "-enable-pretty-printing",
					"ignoreFailures": true
				}
			],
			"logging": {
				"engineLogging": true,
				"programOutput": true
			},
			"preLaunchTask": "minic and qemu run arm32"
		},
		{
			"name": "Qemu Debug minic arm64",
			"type": "cppdbg",
			"request": "launch",
			"program": "${workspaceFolder}/tests/test1-1",
			"args": [],
			// 程序的入口（main函数的入口）是否停止
			"stopAtEntry": true,
			"cwd": "${workspaceFolder}",
			"miDebuggerServerAddress": "localhost:1234",
			"miDebuggerPath": "/usr/bin/gdb-multiarch",
			"environment": [],
			"externalConsole": false,
			"MIMode": "gdb",
			"setupCommands": [
				{
					"description": "pretty printing",
					"text": "-enable-pretty-printing",
					"ignoreFailures": true
				}
			],
			"logging": {
				"engineLogging": true,
				"programOutput": true
			},
			"preLaunchTask": "minic and qemu run arm64"
		},
		{
			"name": "Qemu Debug minic riscv64",
			"type": "cppdbg",
			"request": "launch",
			"program": "${workspaceFolder}/tests/test1-1",
			"args": [],
			// 程序的入口（main函数的入口）是否停止
			"stopAtEntry": true,
			"cwd": "${workspaceFolder}",
			"miDebuggerServerAddress": "localhost:1234",
			"miDebuggerPath": "/usr/bin/gdb-multiarch",
			"environment": [],
			"externalConsole": false,
			"MIMode": "gdb",
			"setupCommands": [
				{
					"description": "pretty printing",
					"text": "-enable-pretty-printing",
					"ignoreFailures": true
				}
			],
			"logging": {
				"engineLogging": true,
				"programOutput": true
			},
			"preLaunchTask": "minic and qemu run riscv64"
		},
		{
			"name": "Qemu Debug Direct arm32",
			"type": "cppdbg",
			"request": "launch",
			"program": "${workspaceFolder}/tests/test1-1",
			"args": [],
			// 程序的入口（main函数的入口）是否停止
			"stopAtEntry": true,
			"cwd": "${workspaceFolder}",
			"miDebuggerServerAddress": "localhost:1234",
			"miDebuggerPath": "/usr/bin/gdb-multiarch",
			"environment": [],
			"externalConsole": false,
			"MIMode": "gdb",
			"setupCommands": [
				{
					"description": "pretty printing",
					"text": "-enable-pretty-printing",
					"ignoreFailures": true
				}
			],
			"logging": {
				"engineLogging": true,
				"programOutput": true
			},
			"preLaunchTask": "direct and qemu run arm32"
		},
		{
			"name": "Qemu Debug Direct arm64",
			"type": "cppdbg",
			"request": "launch",
			"program": "${workspaceFolder}/tests/test1-1",
			"args": [],
			// 程序的入口（main函数的入口）是否停止
			"stopAtEntry": true,
			"cwd": "${workspaceFolder}",
			"miDebuggerServerAddress": "localhost:1234",
			"miDebuggerPath": "/usr/bin/gdb-multiarch",
			"environment": [],
			"externalConsole": false,
			"MIMode": "gdb",
			"setupCommands": [
				{
					"description": "pretty printing",
					"text": "-enable-pretty-printing",
					"ignoreFailures": true
				}
			],
			"logging": {
				"engineLogging": true,
				"programOutput": true
			},
			"preLaunchTask": "direct and qemu run arm64"
		},
		{
			"name": "Qemu Debug Direct riscv64",
			"type": "cppdbg",
			"request": "launch",
			"program": "${workspaceFolder}/tests/test1-1",
			"args": [],
			// 程序的入口（main函数的入口）是否停止
			"stopAtEntry": true,
			"cwd": "${workspaceFolder}",
			"miDebuggerServerAddress": "localhost:1234",
			"miDebuggerPath": "/usr/bin/gdb-multiarch",
			"environment": [],
			"externalConsole": false,
			"MIMode": "gdb",
			"setupCommands": [
				{
					"description": "pretty printing",
					"text": "-enable-pretty-printing",
					"ignoreFailures": true
				}
			],
			"logging": {
				"engineLogging": true,
				"programOutput": true
			},
			"preLaunchTask": "direct and qemu run riscv64"
		}
	]
}
