///
/// @file ArrayType.h
/// @brief 数组类型描述类，实验八新增
///
/// <AUTHOR> (<EMAIL>)
/// @version 1.0
/// @date 2024-11-23
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-11-23 <td>1.0     <td>zenglj  <td>新建
/// </table>
///

#pragma once

#include "Type.h"
#include <vector>

class ArrayType : public Type {

public:
    ///
    /// @brief 获取数组类型
    /// @param elementType 数组元素类型
    /// @param dimensions 数组各维度大小
    /// @return ArrayType*
    ///
    static ArrayType * getType(Type * elementType, const std::vector<int32_t> & dimensions);

    ///
    /// @brief 获取数组元素类型
    /// @return Type*
    ///
    [[nodiscard]] Type * getElementType() const
    {
        return elementType;
    }

    ///
    /// @brief 获取数组维度数量
    /// @return int32_t
    ///
    [[nodiscard]] int32_t getDimensionCount() const
    {
        return static_cast<int32_t>(dimensions.size());
    }

    ///
    /// @brief 获取指定维度的大小
    /// @param index 维度索引
    /// @return int32_t
    ///
    [[nodiscard]] int32_t getDimensionSize(int32_t index) const
    {
        if (index >= 0 && index < static_cast<int32_t>(dimensions.size())) {
            return dimensions[index];
        }
        return -1;
    }

    ///
    /// @brief 获取所有维度大小
    /// @return const std::vector<int32_t>&
    ///
    [[nodiscard]] const std::vector<int32_t> & getDimensions() const
    {
        return dimensions;
    }

    ///
    /// @brief 获取数组总元素个数
    /// @return int32_t
    ///
    [[nodiscard]] int32_t getTotalElements() const
    {
        int32_t total = 1;
        for (int32_t dim : dimensions) {
            if (dim <= 0) return 0; // 形参数组第一维为0
            total *= dim;
        }
        return total;
    }

    ///
    /// @brief 是否是形参数组（第一维为0）
    /// @return bool
    ///
    [[nodiscard]] bool isParameterArray() const
    {
        return !dimensions.empty() && dimensions[0] == 0;
    }

    ///
    /// @brief 获得类型所占内存空间大小
    /// @return int32_t
    ///
    [[nodiscard]] int32_t getSize() const override
    {
        if (isParameterArray()) {
            // 形参数组只占指针大小
            return 4; // ARM32指针大小
        }
        return getTotalElements() * elementType->getSize();
    }

    ///
    /// @brief 获取类型的IR标识符
    /// @return std::string IR标识符
    ///
    [[nodiscard]] std::string toString() const override
    {
        std::string result = elementType->toString();
        for (int32_t dim : dimensions) {
            result += "[" + std::to_string(dim) + "]";
        }
        return result;
    }

private:
    ///
    /// @brief 构造函数
    /// @param elementType 数组元素类型
    /// @param dimensions 数组各维度大小
    ///
    ArrayType(Type * elementType, const std::vector<int32_t> & dimensions) 
        : Type(Type::ArrayTyID), elementType(elementType), dimensions(dimensions)
    {}

    ///
    /// @brief 数组元素类型
    ///
    Type * elementType;

    ///
    /// @brief 数组各维度大小
    ///
    std::vector<int32_t> dimensions;
};
