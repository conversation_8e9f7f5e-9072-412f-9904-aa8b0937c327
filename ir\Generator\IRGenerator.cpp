﻿///
/// @file IRGenerator.cpp
/// @brief AST遍历产生线性IR的源文件
/// <AUTHOR> (<EMAIL>)
/// @version 1.1
/// @date 2024-11-23
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-09-29 <td>1.0     <td>zenglj  <td>新建
/// <tr><td>2024-11-23 <td>1.1     <td>zenglj  <td>表达式版增强
/// </table>
///
#include <cstdint>
#include <cstdio>
#include <unordered_map>
#include <vector>
#include <iostream>

#include "AST.h"
#include "Common.h"
#include "Constant.h"
#include "ConstInt.h"
#include "Function.h"
#include "IRCode.h"
#include "IRGenerator.h"
#include "Module.h"
#include "EntryInstruction.h"
#include "LabelInstruction.h"
#include "ExitInstruction.h"
#include "FuncCallInstruction.h"
#include "BinaryInstruction.h"
#include "MoveInstruction.h"
#include "GotoInstruction.h"
#include "BranchGoToInstruction.h"
#include "ArrayType.h"
#include "PointerType.h"
#include "StoreInstruction.h"
/// @brief 构造函数
/// @param _root AST的根
/// @param _module 符号表
IRGenerator::IRGenerator(ast_node * _root, Module * _module) : root(_root), module(_module)
{
    /* 叶子节点 */

    ast2ir_handlers[ast_operator_type::AST_OP_LEAF_LITERAL_UINT] = &IRGenerator::ir_leaf_node_uint;
    ast2ir_handlers[ast_operator_type::AST_OP_LEAF_VAR_ID] = &IRGenerator::ir_leaf_node_var_id;
    ast2ir_handlers[ast_operator_type::AST_OP_LEAF_TYPE] = &IRGenerator::ir_leaf_node_type;

    /* 表达式运算， 加减 */
    ast2ir_handlers[ast_operator_type::AST_OP_SUB] = &IRGenerator::ir_sub;
    ast2ir_handlers[ast_operator_type::AST_OP_ADD] = &IRGenerator::ir_add;
    /* 表达式运算， 乘除余 */
    ast2ir_handlers[ast_operator_type::AST_OP_MUL] = &IRGenerator::ir_mul;
    ast2ir_handlers[ast_operator_type::AST_OP_DIV] = &IRGenerator::ir_div;
	ast2ir_handlers[ast_operator_type::AST_OP_MOD] = &IRGenerator::ir_mod;
	//单目运算
    ast2ir_handlers[ast_operator_type::AST_OP_NEG] = &IRGenerator::ir_neg;
    // 关系运算
    ast2ir_handlers[ast_operator_type::AST_OP_LE] = &IRGenerator::ir_le;
    ast2ir_handlers[ast_operator_type::AST_OP_LT] = &IRGenerator::ir_lt;
    ast2ir_handlers[ast_operator_type::AST_OP_GE] = &IRGenerator::ir_ge;
    ast2ir_handlers[ast_operator_type::AST_OP_GT] = &IRGenerator::ir_gt;
    ast2ir_handlers[ast_operator_type::AST_OP_NOT] = &IRGenerator::ir_not;
    ast2ir_handlers[ast_operator_type::AST_OP_EQUALS] = &IRGenerator::ir_equals;
    ast2ir_handlers[ast_operator_type::AST_OP_NOT_EQUALS] = &IRGenerator::ir_not_equals;


    /* 语句 */
    ast2ir_handlers[ast_operator_type::AST_OP_ASSIGN] = &IRGenerator::ir_assign;
    ast2ir_handlers[ast_operator_type::AST_OP_RETURN] = &IRGenerator::ir_return;
    ast2ir_handlers[ast_operator_type::AST_OP_IF] = &IRGenerator::ir_if;
    ast2ir_handlers[ast_operator_type::AST_OP_WHILE] = &IRGenerator::ir_while;
    ast2ir_handlers[ast_operator_type::AST_OP_BREAK] = &IRGenerator::ir_break;
    ast2ir_handlers[ast_operator_type::AST_OP_CONTINUE] = &IRGenerator::ir_continue;
    /* 函数调用 */
    ast2ir_handlers[ast_operator_type::AST_OP_FUNC_CALL] = &IRGenerator::ir_function_call;

    /* 函数定义 */
    ast2ir_handlers[ast_operator_type::AST_OP_FUNC_DEF] = &IRGenerator::ir_function_define;
    ast2ir_handlers[ast_operator_type::AST_OP_FUNC_FORMAL_PARAMS] = &IRGenerator::ir_function_formal_params;

    /* 变量定义语句 */
    ast2ir_handlers[ast_operator_type::AST_OP_DECL_STMT] = &IRGenerator::ir_declare_statment;
    ast2ir_handlers[ast_operator_type::AST_OP_VAR_DECL] = &IRGenerator::ir_variable_declare;

    /* 数组访问，实验八新增 */
    ast2ir_handlers[ast_operator_type::AST_OP_ARRAY_ACCESS] = &IRGenerator::ir_array_access;

    /* 语句块 */
    ast2ir_handlers[ast_operator_type::AST_OP_BLOCK] = &IRGenerator::ir_block;

    /* 编译单元 */
    ast2ir_handlers[ast_operator_type::AST_OP_COMPILE_UNIT] = &IRGenerator::ir_compile_unit;
}

/// @brief 遍历抽象语法树产生线性IR，保存到IRCode中
/// @param root 抽象语法树
/// @param IRCode 线性IR
/// @return true: 成功 false: 失败
bool IRGenerator::run()
{
    ast_node * node;

    // 从根节点进行遍历
    node = ir_visit_ast_node(root);

    return node != nullptr;
}

/// @brief 根据AST的节点运算符查找对应的翻译函数并执行翻译动作
/// @param node AST节点
/// @return 成功返回node节点，否则返回nullptr
ast_node * IRGenerator::ir_visit_ast_node(ast_node * node)
{
    // 空节点
    if (nullptr == node) {
        return nullptr;
    }

    bool result;

    std::unordered_map<ast_operator_type, ast2ir_handler_t>::const_iterator pIter;
    pIter = ast2ir_handlers.find(node->node_type);
    if (pIter == ast2ir_handlers.end()) {
        // 没有找到，则说明当前不支持
        result = (this->ir_default)(node);
    } else {
        result = (this->*(pIter->second))(node);
    }

    if (!result) {
        // 语义解析错误，则出错返回
        node = nullptr;
    }

    return node;
}


/// @brief 未知节点类型的节点处理
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_default(ast_node * node)
{
    // 未知的节点
    printf("Unkown node(%d)\n", (int) node->node_type);
    return true;
}

/// @brief 编译单元AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_compile_unit(ast_node * node)
{
    module->setCurrentFunction(nullptr);

    for (auto son: node->sons) {

        // 遍历编译单元，要么是函数定义，要么是语句
        ast_node * son_node = ir_visit_ast_node(son);
        if (!son_node) {
            // TODO 自行追加语义错误处理
            return false;
        }
    }

    return true;
}

/// @brief 函数定义AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_function_define(ast_node * node)
{
    //实验七：函数定义IR生成，支持int/void返回类型和多个形参
    if (module->getCurrentFunction()) return false; // 不允许嵌套函数定义

    // 解析AST节点：返回类型、函数名、形参列表、函数体
    ast_node * type_node = node->sons[0];
    ast_node * name_node = node->sons[1];
    ast_node * param_node = node->sons[2];
    ast_node * block_node = node->sons[3];

    // 创建新函数
    Function * newFunc = module->newFunction(name_node->name, type_node->type);
    if (!newFunc) return false; // 函数已存在

    // 创新：设置当前函数并进入作用域（为后续RAII模式做准备）
    module->setCurrentFunction(newFunc);
    module->enterScope();

    InterCode & irCode = newFunc->getInterCode();
    irCode.addInst(new EntryInstruction(newFunc));

    // 创建函数出口标签
    LabelInstruction * exitLabelInst = new LabelInstruction(newFunc);
    newFunc->setExitLabel(exitLabelInst);

    //实验七：创新链式处理形参，支持多个形参
    if (param_node) {
        // 创新：使用现代C++范围for循环，一行完成形参添加
        for (const auto &param : param_node->sons) {
            // 检查是否是数组参数
            bool isArrayParam = false;
            for (const auto &child : param->sons) {
                if (child->name == "__ARRAY_PARAM_MARKER__") {
                    isArrayParam = true;
                    break;
                }
            }

            Type * paramType = param->type;
            if (isArrayParam) {
                // 数组参数：正确处理多维数组的维度信息
                // 按照DragonIR规范：第一维为0，后续维度使用实际大小
                std::vector<int> dimensions;
                dimensions.push_back(0); // 第一维为0（表示指针）

                // 收集其他维度的大小
                for (const auto &child : param->sons) {
                    if (child->name != "__ARRAY_PARAM_MARKER__") {
                        // 检查是否是维度节点（常量表达式）
                        if (child->node_type == ast_operator_type::AST_OP_LEAF_LITERAL_UINT) {
                            dimensions.push_back(static_cast<int>(child->integer_val));
                        }
                        // 也可能是其他类型的常量表达式，尝试获取值
                        else if (child->val && dynamic_cast<ConstInt*>(child->val)) {
                            ConstInt* constInt = dynamic_cast<ConstInt*>(child->val);
                            dimensions.push_back(constInt->getVal());
                        }
                    }
                }

                paramType = ArrayType::getType(IntegerType::getTypeInt(), dimensions);
            }

            newFunc->addParam(new FormalParam(paramType, param->name));
        }

        if (!ir_function_formal_params(param_node)) return false;
        node->blockInsts.addInst(param_node->blockInsts);
    }

    //实验七：创新条件表达式处理返回值变量，确保函数只有一个出口
    LocalVariable * retValue = type_node->type->isVoidType() ? nullptr :
        static_cast<LocalVariable *>(module->newVarValue(type_node->type));

    //实验七：main函数返回值初始化为0（创新：条件表达式优化）
    if (retValue && name_node->name == "main") {
        irCode.addInst(new MoveInstruction(newFunc, retValue, module->newConstInt(0)));
    }
    newFunc->setReturnValue(retValue);

    // 处理函数体
    block_node->needScope = false;
    if (!ir_block(block_node)) return false;

    // 生成IR指令
    node->blockInsts.addInst(block_node->blockInsts);
    irCode.addInst(node->blockInsts);
    irCode.addInst(exitLabelInst);
    irCode.addInst(new ExitInstruction(newFunc, retValue));

    // 恢复外部状态
    module->setCurrentFunction(nullptr);
    module->leaveScope();
    return true;
}
/// @brief 形式参数AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_function_formal_params(ast_node * node)
{
    //实验七：形参处理，每个形参对应两个值（实参值+局部变量）
    Function * currentFunc = module->getCurrentFunction();
    if (!currentFunc) return false;

    const auto & params = node->sons;
    const auto & formalParams = currentFunc->getParams();
    if (params.size() != formalParams.size()) return false;

    // 创新：使用范围for循环和索引，一行完成参数处理
    InterCode & irCode = currentFunc->getInterCode();
    for (size_t i = 0; i < params.size(); ++i) {
        // 检查是否是数组参数
        bool isArrayParam = false;
        for (const auto &child : params[i]->sons) {
            if (child->name == "__ARRAY_PARAM_MARKER__") {
                isArrayParam = true;
                break;
            }
        }

        Type * localVarType = params[i]->type;
        if (isArrayParam) {
            // 数组参数的局部变量：使用与函数参数相同的类型
            // 这样确保局部变量和函数参数有相同的维度信息
            localVarType = formalParams[i]->getType();
        }

        irCode.addInst(new MoveInstruction(currentFunc,
            module->newVarValue(localVarType, params[i]->name), formalParams[i]));
    }
    return true;
}


/// @brief 函数调用AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_function_call(ast_node * node)
{
    //实验七：函数调用IR生成，支持多个实参
    Function * currentFunc = module->getCurrentFunction();
    std::string funcName = node->sons[0]->name;
    ast_node * paramsNode = node->sons[1];

    // 查找被调用函数
    auto calledFunction = module->findFunction(funcName);
    if (!calledFunction) {
        minic_log(LOG_ERROR, "函数(%s)未定义或声明", funcName.c_str());
        return false;
    }
    currentFunc->setExistFuncCall(true);

    //实验七：创新函数式风格处理多个实参
    std::vector<Value *> realParams;
    const auto & args = paramsNode->sons;

    // 创新：一行更新最大参数计数
    if (!args.empty() && static_cast<int>(args.size()) > currentFunc->getMaxFuncCallArgCnt()) {
        currentFunc->setMaxFuncCallArgCnt(static_cast<int32_t>(args.size()));
    }

    // 创新：使用现代C++范围for循环处理实参
    for (const auto & arg : args) {
        ast_node * temp = ir_visit_ast_node(arg);
        if (temp) {
            Value * paramValue = temp->val;

            // 检查是否需要数组到指针的转换或指针解引用
            if (paramValue->getType()->isArrayType()) {
                // 数组参数：直接使用数组变量，并添加必要的IR指令
                node->blockInsts.addInst(temp->blockInsts);
            } else if (paramValue->getType()->isPointerType() &&
                       arg->node_type == ast_operator_type::AST_OP_ARRAY_ACCESS) {
                // 数组访问结果是指针，但函数参数期望值，需要解引用
                // 先添加子表达式的IR指令
                node->blockInsts.addInst(temp->blockInsts);

                class LoadInstruction : public MoveInstruction {
                public:
                    LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                        : MoveInstruction(_func, _result, _srcVal1) {}

                    void toString(std::string & str) override {
                        Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                        str = dstVal->getIRName() + " = *" + srcVal->getIRName();
                    }
                };

                Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
                LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, paramValue);
                node->blockInsts.addInst(loadInst);
                paramValue = tempVar;
            } else {
                // 普通参数，添加子表达式的IR指令
                node->blockInsts.addInst(temp->blockInsts);
            }

            realParams.push_back(paramValue);
        } else {
            return false;
        }
    }

    // 参数个数检查
    if (realParams.size() != calledFunction->getParams().size()) {
        minic_log(LOG_ERROR, "函数(%s)参数个数不匹配", funcName.c_str());
        return false;
    }

    // 创建函数调用指令
    FuncCallInstruction * funcCallInst = new FuncCallInstruction(
        currentFunc, calledFunction, realParams, calledFunction->getReturnType());
    node->blockInsts.addInst(funcCallInst);
    node->val = funcCallInst;
    return true;
}

/// @brief 语句块（含函数体）AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_block(ast_node * node)
{
    // 进入作用域
    if (node->needScope) {
        module->enterScope();
    }

    std::vector<ast_node *>::iterator pIter;
    for (pIter = node->sons.begin(); pIter != node->sons.end(); ++pIter) {

        // 遍历Block的每个语句，进行显示或者运算
        ast_node * temp = ir_visit_ast_node(*pIter);
        if (!temp) {
            return false;
        }

        node->blockInsts.addInst(temp->blockInsts);
    }

    // 离开作用域
    if (node->needScope) {
        module->leaveScope();
    }

    return true;
}
// 假设 currentFunc 是当前函数指针，inputVal 是需要转布尔的整型值
Value* IRGenerator::int_to_bool(Value* val) {
    Function* func = module->getCurrentFunction();
    ConstInt * zero = module->newConstInt(0);
    BinaryInstruction* cmpInst = new BinaryInstruction(
        func,
        IRInstOperator::IRINST_OP_NE_I,  // val != 0
        val,
        zero,
        IntegerType::getTypeBool()
    );
    func->getInterCode().addInst(cmpInst);
    return cmpInst;
}



bool IRGenerator::ir_if(ast_node* node) {
    Function* currentFunc = module->getCurrentFunction();

    // 获取if结构的3个子节点：条件表达式、then块和可选的else块
    ast_node* cond_node = node->sons[0];
    ast_node* then_node = node->sons[1];
    ast_node* else_node = node->sons[2];

    // 创建流程控制所需的标签指令
    // thenLabel：条件为真跳转；endLabel：整个if语句结束的跳转点；
    // elseLabel：条件为假跳转（如果没有else块，则跳转到endLabel）
    LabelInstruction* thenLabel = new LabelInstruction(currentFunc);
    LabelInstruction* endLabel = new LabelInstruction(currentFunc);
    LabelInstruction* elseLabel = else_node ? new LabelInstruction(currentFunc) : endLabel;

    // 对条件表达式生成IR，同时插入条件跳转指令（根据条件真/假分别跳转）
    if (!ir_visit_bool_expr(cond_node, thenLabel, elseLabel)) {
        return false;  // 如果表达式翻译失败则直接退出
    }
    // 将条件表达式生成的代码添加到当前if语句的指令块中
    node->blockInsts.addInst(cond_node->blockInsts);

    // 添加then块的起始标签并生成其对应的代码块
    node->blockInsts.addInst(thenLabel);
    if (!ir_visit_ast_node(then_node)) {
        return false;
    }
    node->blockInsts.addInst(then_node->blockInsts);

    // then块代码结束后跳转到end，避免直接执行else部分
    node->blockInsts.addInst(new GotoInstruction(currentFunc, endLabel));

    // 如果存在else块（包括else if结构），处理其代码逻辑
    if (else_node) {
        node->blockInsts.addInst(elseLabel);

        if (else_node->node_type == ast_operator_type::AST_OP_IF) {
            // 递归处理else if分支：其实就是嵌套if语句
            if (!ir_if(else_node)) {
                return false;
            }
            node->blockInsts.addInst(else_node->blockInsts);
        } else {
            // 处理普通的else代码块
            if (!ir_visit_ast_node(else_node)) {
                return false;
            }
            node->blockInsts.addInst(else_node->blockInsts);

            // 此处可省略goto endLabel，因为elseLabel是唯一入口且if语句之后无额外内容
            node->blockInsts.addInst(new GotoInstruction(currentFunc, endLabel));
        }
    }

    // 添加整个if语句结束的标签
    node->blockInsts.addInst(endLabel);

    return true;
}

bool IRGenerator::ir_while(ast_node* node) {
    Function* currentFunc = module->getCurrentFunction();

    // while语句结构：sons[0] 是判断条件，sons[1] 是循环体
    ast_node* cond_node = node->sons[0];
    ast_node* body_node = node->sons[1];

    // 创建三个标签：
    // condLabel：用于跳转回来重新判断条件（continue语句用）
    // bodyLabel：当条件为真时进入循环体
    // endLabel：循环结束后跳转的位置（break语句用）
    LabelInstruction* condLabel = new LabelInstruction(currentFunc);
    LabelInstruction* bodyLabel = new LabelInstruction(currentFunc);
    LabelInstruction* endLabel = new LabelInstruction(currentFunc);

    // 保存当前函数中的break/continue目标标签，以支持嵌套循环
    LabelInstruction* oldBreakLabel = currentBreakLabel;
    LabelInstruction* oldContinueLabel = currentContinueLabel;

    // 设置当前循环的break和continue目标标签
    currentBreakLabel = endLabel;
    currentContinueLabel = condLabel;

    // 首先跳转到条件判断标签（而不是直接开始循环体）
    node->blockInsts.addInst(new GotoInstruction(currentFunc, condLabel));

    // 条件判断标签位置
    node->blockInsts.addInst(condLabel);

    // 生成条件判断的IR指令，若为真则跳到bodyLabel，否则跳转endLabel退出循环
    if (!ir_visit_bool_expr(cond_node, bodyLabel, endLabel)) {
        return false;
    }
    node->blockInsts.addInst(cond_node->blockInsts);

    // 条件为真时，跳入循环体开始位置
    node->blockInsts.addInst(bodyLabel);

    // 翻译循环体的语句块
    if (!ir_visit_ast_node(body_node)) {
        return false;
    }
    node->blockInsts.addInst(body_node->blockInsts);

    // 循环体执行完后跳回去判断条件，形成循环
    node->blockInsts.addInst(new GotoInstruction(currentFunc, condLabel));

    // 循环结束标签
    node->blockInsts.addInst(endLabel);

    // 恢复原有的break和continue目标标签
    currentBreakLabel = oldBreakLabel;
    currentContinueLabel = oldContinueLabel;

    return true;
}

bool IRGenerator::ir_break(ast_node* node) {
    Function* currentFunc = module->getCurrentFunction();

    // 如果当前没有break目标（即不在循环中），报错
    if (!currentBreakLabel) {
        fprintf(stderr, "Error: 'break' used outside of loop.\n");
        return false;
    }

    // 插入一条跳转到循环外部的指令（即循环尾部的endLabel）
    node->blockInsts.addInst(new GotoInstruction(currentFunc, currentBreakLabel));

    return true;
}



bool IRGenerator::ir_continue(ast_node* node) {
    Function* currentFunc = module->getCurrentFunction();

    // 如果当前没有continue目标（即不在循环中），报错
    if (!currentContinueLabel) {
        fprintf(stderr, "Error: 'continue' used outside of loop.\n");
        return false;
    }

    // 插入跳转到条件判断的标签，以重新判断是否继续执行循环
    node->blockInsts.addInst(new GotoInstruction(currentFunc, currentContinueLabel));

    return true;
}


/// @brief 整数加法AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_add(ast_node * node)
{
    ast_node * src1_node = node->sons[0];
    ast_node * src2_node = node->sons[1];

    // 加法节点，左结合，先计算左节点，后计算右节点

    // 加法的左边操作数
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 某个变量没有定值
        return false;
    }

    // 加法的右边操作数
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 某个变量没有定值
        return false;
    }

    // 这里只处理整型的数据，如需支持实数，则需要针对类型进行处理

    // 先添加子表达式的IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 检查操作数是否需要解引用
    Value * leftOperand = left->val;
    Value * rightOperand = right->val;

    // 如果左操作数是指针类型，需要解引用
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 如果右操作数是指针类型，需要解引用
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    BinaryInstruction * addInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_ADD_I,
                                                        leftOperand,
                                                        rightOperand,
                                                        IntegerType::getTypeInt());
    node->blockInsts.addInst(addInst);

    node->val = addInst;

    return true;
}

/// @brief 整数减法AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_sub(ast_node * node)
{
    ast_node * src1_node = node->sons[0];
    ast_node * src2_node = node->sons[1];

    // 加法节点，左结合，先计算左节点，后计算右节点

    // 加法的左边操作数
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 某个变量没有定值
        return false;
    }

    // 加法的右边操作数
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 某个变量没有定值
        return false;
    }

    // 这里只处理整型的数据，如需支持实数，则需要针对类型进行处理

    // 先添加子表达式的IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 检查操作数是否需要解引用
    Value * leftOperand = left->val;
    Value * rightOperand = right->val;

    // 如果左操作数是指针类型，需要解引用
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 如果右操作数是指针类型，需要解引用
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    BinaryInstruction * subInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_SUB_I,
                                                        leftOperand,
                                                        rightOperand,
                                                        IntegerType::getTypeInt());

    // 添加减法指令
    node->blockInsts.addInst(subInst);

    node->val = subInst;

    return true;
}
//单目运算符-
bool IRGenerator::ir_neg(ast_node * node)
{
    ast_node * expr_node = node->sons[0];
    ast_node * val_node = ir_visit_ast_node(expr_node);
    if (!val_node) return false;

    // 创建一个常量 0
    ConstInt * zero = module->newConstInt(0);

    // 创建二元减法指令 `0 - val`
    BinaryInstruction * subInst = new BinaryInstruction(
        module->getCurrentFunction(),
        IRInstOperator::IRINST_OP_NEG_I, // 使用整数减法
        zero,  // 左操作数是 0
        val_node->val, // 右操作数是 val
        IntegerType::getTypeInt() // 结果类型
    );

    // 先添加子表达式 IR 指令
    node->blockInsts.addInst(val_node->blockInsts);

    // 再添加 `0 - val` 的指令
    node->blockInsts.addInst(subInst);

    // 将结果绑定到当前节点
    node->val = subInst;

    return true;
}



/// @brief 整数乘法AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_mul(ast_node * node)
{
    ast_node * src1_node = node->sons[0];
    ast_node * src2_node = node->sons[1];

    // 加法节点，左结合，先计算左节点，后计算右节点

    // 加法的左边操作数
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 某个变量没有定值
        return false;
    }

    // 加法的右边操作数
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 某个变量没有定值
        return false;
    }

    // 这里只处理整型的数据，如需支持实数，则需要针对类型进行处理

    // 先添加子表达式的IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 检查操作数是否需要解引用
    Value * leftOperand = left->val;
    Value * rightOperand = right->val;

    // 如果左操作数是指针类型，需要解引用
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 如果右操作数是指针类型，需要解引用
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    BinaryInstruction * mulInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_MUL_I,
                                                        leftOperand,
                                                        rightOperand,
                                                        IntegerType::getTypeInt());

    // 添加乘法指令
    node->blockInsts.addInst(mulInst);

    node->val = mulInst;

    return true;
}
/// @brief 整数除法AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_div(ast_node * node)
{
    ast_node * src1_node = node->sons[0];
    ast_node * src2_node = node->sons[1];

    // 加法节点，左结合，先计算左节点，后计算右节点

    // 加法的左边操作数
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 某个变量没有定值
        return false;
    }

    // 加法的右边操作数
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 某个变量没有定值
        return false;
    }

    // 这里只处理整型的数据，如需支持实数，则需要针对类型进行处理

    // 先添加子表达式的IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 检查操作数是否需要解引用
    Value * leftOperand = left->val;
    Value * rightOperand = right->val;

    // 如果左操作数是指针类型，需要解引用
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 如果右操作数是指针类型，需要解引用
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    BinaryInstruction * divInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_DIV_I,
                                                        leftOperand,
                                                        rightOperand,
                                                        IntegerType::getTypeInt());

    // 添加除法指令
    node->blockInsts.addInst(divInst);

    node->val = divInst;

    return true;
}
/// @brief 整数取余AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_mod(ast_node * node)
{
    ast_node * src1_node = node->sons[0];
    ast_node * src2_node = node->sons[1];

    // 加法节点，左结合，先计算左节点，后计算右节点

    // 加法的左边操作数
    ast_node * left = ir_visit_ast_node(src1_node);
    if (!left) {
        // 某个变量没有定值
        return false;
    }

    // 加法的右边操作数
    ast_node * right = ir_visit_ast_node(src2_node);
    if (!right) {
        // 某个变量没有定值
        return false;
    }

    // 这里只处理整型的数据，如需支持实数，则需要针对类型进行处理

    // 先添加子表达式的IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 检查操作数是否需要解引用
    Value * leftOperand = left->val;
    Value * rightOperand = right->val;

    // 如果左操作数是指针类型，需要解引用
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 如果右操作数是指针类型，需要解引用
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    BinaryInstruction * modInst = new BinaryInstruction(module->getCurrentFunction(),
                                                        IRInstOperator::IRINST_OP_MOD_I,
                                                        leftOperand,
                                                        rightOperand,
                                                        IntegerType::getTypeInt());

    // 添加取模指令
    node->blockInsts.addInst(modInst);

    node->val = modInst;

    return true;
}
/// @brief 小于比较AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_lt(ast_node* node)
{
    // 提取左右子表达式
    ast_node* src1_node = node->sons[0];
    ast_node* src2_node = node->sons[1];

    // 递归生成左操作数IR
    ast_node* left = ir_visit_ast_node(src1_node);
    if (!left) return false;

    // 递归生成右操作数IR
    ast_node* right = ir_visit_ast_node(src2_node);
    if (!right) return false;

    // 合并左右操作数的中间代码
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 处理左操作数：如果是指针类型（数组访问），需要解引用
    Value* leftOperand = left->val;
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 处理右操作数：如果是指针类型，需要解引用
    Value* rightOperand = right->val;
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    // 构造小于比较的IR指令，类型为布尔类型（i1）
    BinaryInstruction* cmpInst = new BinaryInstruction(
        module->getCurrentFunction(),
        IRInstOperator::IRINST_OP_LT_I,
        leftOperand,
        rightOperand,
        IntegerType::getTypeBool()
    );

    // 添加当前比较指令
    node->blockInsts.addInst(cmpInst);

    // 保存结果（用于后续使用）
    node->val = cmpInst;

    return true;
}

/// @brief 小于等于（<=）表达式翻译成IR
bool IRGenerator::ir_le(ast_node* node)
{
    ast_node* src1_node = node->sons[0];
    ast_node* src2_node = node->sons[1];

    ast_node* left = ir_visit_ast_node(src1_node);
    if (!left) return false;

    ast_node* right = ir_visit_ast_node(src2_node);
    if (!right) return false;

    // 构造LE比较指令
    BinaryInstruction* cmpInst = new BinaryInstruction(
        module->getCurrentFunction(),
        IRInstOperator::IRINST_OP_LE_I,
        left->val,
        right->val,
        IntegerType::getTypeBool()
    );

    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);
    node->blockInsts.addInst(cmpInst);
    node->val = cmpInst;

    return true;
}


/// @brief 大于（>）表达式翻译为IR
bool IRGenerator::ir_gt(ast_node* node)
{
    ast_node* src1_node = node->sons[0];
    ast_node* src2_node = node->sons[1];

    ast_node* left = ir_visit_ast_node(src1_node);
    if (!left) return false;

    ast_node* right = ir_visit_ast_node(src2_node);
    if (!right) return false;

    // 添加子表达式的IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 处理左操作数：如果是指针类型（数组访问），需要解引用
    Value* leftOperand = left->val;
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 处理右操作数：如果是指针类型，需要解引用
    Value* rightOperand = right->val;
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    BinaryInstruction* cmpInst = new BinaryInstruction(
        module->getCurrentFunction(),
        IRInstOperator::IRINST_OP_GT_I,
        leftOperand,
        rightOperand,
        IntegerType::getTypeBool()
    );

    node->blockInsts.addInst(cmpInst);
    node->val = cmpInst;

    return true;
}


/// @brief 大于等于（>=）表达式翻译为IR
bool IRGenerator::ir_ge(ast_node* node)
{
    ast_node* src1_node = node->sons[0];
    ast_node* src2_node = node->sons[1];

    ast_node* left = ir_visit_ast_node(src1_node);
    if (!left) return false;

    ast_node* right = ir_visit_ast_node(src2_node);
    if (!right) return false;

    BinaryInstruction* cmpInst = new BinaryInstruction(
        module->getCurrentFunction(),
        IRInstOperator::IRINST_OP_GE_I,
        left->val,
        right->val,
        IntegerType::getTypeBool()
    );

    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);
    node->blockInsts.addInst(cmpInst);
    node->val = cmpInst;

    return true;
}



/// @brief 等于比较AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_equals(ast_node* node)
{
    ast_node* src1_node = node->sons[0];
    ast_node* src2_node = node->sons[1];

    // 先访问左子节点
    ast_node* left = ir_visit_ast_node(src1_node);
    if (left == nullptr)
        return false;

    // 再访问右子节点
    ast_node* right = ir_visit_ast_node(src2_node);
    if (right == nullptr)
        return false;

    // 添加子表达式的IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 处理左操作数：如果是指针类型（数组访问），需要解引用
    Value* leftOperand = left->val;
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 处理右操作数：如果是指针类型，需要解引用
    Value* rightOperand = right->val;
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    // 创建等于比较指令
    BinaryInstruction* cmpInst = new BinaryInstruction(
        module->getCurrentFunction(),
        IRInstOperator::IRINST_OP_EQ_I,
        leftOperand,
        rightOperand,
        IntegerType::getTypeBool()
    );

    // 添加比较指令
    node->blockInsts.addInst(cmpInst);

    node->val = cmpInst;

    return true;
}


/// @brief 不等于比较AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_not_equals(ast_node* node)
{
    ast_node* src1_node = node->sons[0];
    ast_node* src2_node = node->sons[1];

    // 访问左子节点
    ast_node* left = ir_visit_ast_node(src1_node);
    if (left == nullptr)
        return false;

    // 访问右子节点
    ast_node* right = ir_visit_ast_node(src2_node);
    if (right == nullptr)
        return false;

    // 添加子表达式的IR指令
    node->blockInsts.addInst(left->blockInsts);
    node->blockInsts.addInst(right->blockInsts);

    // 处理左操作数：如果是指针类型（数组访问），需要解引用
    Value* leftOperand = left->val;
    if (leftOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, leftOperand);
        node->blockInsts.addInst(loadInst);
        leftOperand = tempVar;
    }

    // 处理右操作数：如果是指针类型，需要解引用
    Value* rightOperand = right->val;
    if (rightOperand->getType()->isPointerType()) {
        // 创建解引用指令
        class LoadInstruction : public MoveInstruction {
        public:
            LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = dstVal->getIRName() + " = *" + srcVal->getIRName();
            }
        };

        Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
        LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
        node->blockInsts.addInst(loadInst);
        rightOperand = tempVar;
    }

    // 生成不等于比较指令
    BinaryInstruction* cmpInst = new BinaryInstruction(
        module->getCurrentFunction(),
        IRInstOperator::IRINST_OP_NE_I,
        leftOperand,
        rightOperand,
        IntegerType::getTypeBool()
    );

    // 添加比较指令
    node->blockInsts.addInst(cmpInst);

    node->val = cmpInst;

    return true;
}

/// @brief 逻辑与运算AST节点转换成线性中间IR（短路求值）
/// @param node AST节点
/// @param trueLabel 条件为真时跳转标签
/// @param falseLabel 条件为假时跳转标签
/// @return 是否翻译成功
bool IRGenerator::ir_and(ast_node* node, LabelInstruction* trueLabel, LabelInstruction* falseLabel)
{
    ast_node* left = node->sons[0];
    ast_node* right = node->sons[1];

    LabelInstruction* leftTrueLabel = new LabelInstruction(module->getCurrentFunction());

    if (ir_visit_bool_expr(left, leftTrueLabel, falseLabel) == false)
        return false;

    // 添加左侧表达式的中间代码
    node->blockInsts.addInst(left->blockInsts);

    node->blockInsts.addInst(leftTrueLabel);

    if (ir_visit_bool_expr(right, trueLabel, falseLabel) == false)
        return false;

    // 添加右侧表达式的中间代码
    node->blockInsts.addInst(right->blockInsts);

    return true;
}


/// @brief 逻辑或运算AST节点转换成线性中间IR（短路求值）
/// @param node AST节点
/// @param trueLabel 条件为真时跳转标签
/// @param falseLabel 条件为假时跳转标签
/// @return 是否翻译成功
bool IRGenerator::ir_or(ast_node* node, LabelInstruction* trueLabel, LabelInstruction* falseLabel)
{
    ast_node* left = node->sons[0];
    ast_node* right = node->sons[1];

    LabelInstruction* leftFalseLabel = new LabelInstruction(module->getCurrentFunction());

    if (ir_visit_bool_expr(left, trueLabel, leftFalseLabel) == false)
        return false;

    // 添加左侧表达式的中间代码
    node->blockInsts.addInst(left->blockInsts);

    node->blockInsts.addInst(leftFalseLabel);

    if (ir_visit_bool_expr(right, trueLabel, falseLabel) == false)
        return false;

    // 添加右侧表达式的中间代码
    node->blockInsts.addInst(right->blockInsts);

    return true;
}

/// @brief 逻辑非条件表达式 AST 节点翻译成线性中间 IR（短路求值）
/// @param node 逻辑非 AST 节点，只有一个子表达式
/// @param trueLabel 当前表达式为真时跳转的目标标签
/// @param falseLabel 当前表达式为假时跳转的目标标签
/// @return 是否翻译成功，true 为成功，false 为失败
bool IRGenerator::ir_not_cond(ast_node* node, LabelInstruction* trueLabel, LabelInstruction* falseLabel)
{
    // 取逻辑非表达式的子节点
    ast_node* child = node->sons[0];

    // 逻辑非的短路求值关键：递归生成子表达式的条件跳转IR
    // 但交换trueLabel和falseLabel，实现非的效果（真变假，假变真）
    if (!ir_visit_bool_expr(child, falseLabel, trueLabel)) return false;

    // 将子表达式生成的IR添加到当前节点的IR块中
    node->blockInsts.addInst(child->blockInsts);

    return true;
}

/// @brief 逻辑非表达式 AST 节点翻译成线性中间 IR（非条件短路，计算值）
/// @param node 逻辑非 AST 节点
/// @return 是否翻译成功，true 为成功，false 为失败
bool IRGenerator::ir_not(ast_node* node) {
    // 取逻辑非表达式的子节点
    ast_node* expr_node = node->sons[0];

    // 递归翻译子表达式，得到其值所在的AST节点
    ast_node* val_node = ir_visit_ast_node(expr_node);
    if (!val_node) return false;

    // 生成常量 0，用于与子表达式结果比较
    ConstInt* zero = module->newConstInt(0);

    // 生成比较指令：判断子表达式值是否等于 0，等价于逻辑非运算
    BinaryInstruction* cmpInst = new BinaryInstruction(
        module->getCurrentFunction(),        // 当前函数上下文
        IRInstOperator::IRINST_OP_EQ_I,      // 比较操作符：等于（==）
        val_node->val,                       // 左操作数：子表达式的值
        zero,                               // 右操作数：常量0
        IntegerType::getTypeBool()           // 结果类型：布尔类型
    );

    // 将子表达式的生成IR和本次比较指令依次加入当前节点的IR块
    node->blockInsts.addInst(val_node->blockInsts);
    node->blockInsts.addInst(cmpInst);

    // 设置当前节点的值为比较指令的结果（逻辑非结果）
    node->val = cmpInst;
    return true;
}

/// @brief 访问布尔表达式 AST 节点，递归生成对应的短路求值 IR
/// @param node 当前布尔表达式AST节点
/// @param trueLabel 当前表达式求值为真的跳转标签
/// @param falseLabel 当前表达式求值为假的跳转标签
/// @return 是否生成成功
bool IRGenerator::ir_visit_bool_expr(ast_node* node, LabelInstruction* trueLabel, LabelInstruction* falseLabel)
{
    // 防御式编程：空节点直接失败
    if (!node) return false;

    switch (node->node_type)
    {
        case ast_operator_type::AST_OP_AND:
            // 逻辑与运算，调用对应短路求值生成函数
            return ir_and(node, trueLabel, falseLabel);

        case ast_operator_type::AST_OP_OR:
            // 逻辑或运算，调用对应短路求值生成函数
            return ir_or(node, trueLabel, falseLabel);

        case ast_operator_type::AST_OP_NOT:
            // 逻辑非运算，调用对应短路求值生成函数
            return ir_not_cond(node, trueLabel, falseLabel);

        default:
        {
            // 其他非逻辑运算表达式，先生成普通表达式IR
            if (!ir_visit_ast_node(node)) {
                return false;
            }

            // 取表达式的值
            Value* boolVal = node->val;

            // 如果表达式结果是指针类型（比如数组访问），需要先解引用
            if (boolVal->getType()->isPointerType()) {
                // 创建解引用指令
                class LoadInstruction : public MoveInstruction {
                public:
                    LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                        : MoveInstruction(_func, _result, _srcVal1) {}

                    void toString(std::string & str) override {
                        Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                        str = dstVal->getIRName() + " = *" + srcVal->getIRName();
                    }
                };

                Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
                LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, boolVal);
                node->blockInsts.addInst(loadInst);
                boolVal = tempVar;
            }

            // 生成条件跳转指令：
            // 如果boolVal为真跳转trueLabel，否则跳转falseLabel
            node->blockInsts.addInst(new BranchGoToInstruction(
                module->getCurrentFunction(),
                boolVal,
                trueLabel,
                falseLabel
            ));
            return true;
        }
    }
    return true;
}



//截至
/// @brief 赋值AST节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_assign(ast_node * node)
{
    ast_node * son1_node = node->sons[0];
    ast_node * son2_node = node->sons[1];

    // 赋值节点，自右往左运算

    // 赋值运算符的左侧操作数
    ast_node * left = ir_visit_ast_node(son1_node);
    if (!left) {
        // 某个变量没有定值
        // 这里缺省设置变量不存在则创建，因此这里不会错误
        return false;
    }

    // 赋值运算符的右侧操作数
    ast_node * right = ir_visit_ast_node(son2_node);
    if (!right) {
        // 某个变量没有定值
        return false;
    }

    // 这里只处理整型的数据，如需支持实数，则需要针对类型进行处理

    // 检查左值是否是数组访问
    if (son1_node->node_type == ast_operator_type::AST_OP_ARRAY_ACCESS) {
        // 数组访问赋值：需要生成解引用操作 *address = value
        // left->val 是地址，需要解引用

        // 先添加子表达式的IR指令
        node->blockInsts.addInst(right->blockInsts);
        node->blockInsts.addInst(left->blockInsts);

        // 检查右值是否也需要解引用
        Value * rightOperand = right->val;
        if (son2_node->node_type == ast_operator_type::AST_OP_ARRAY_ACCESS) {
            // 右值也是数组访问，需要先解引用读取值
            class LoadInstruction : public MoveInstruction {
            public:
                LoadInstruction(Function * _func, Value * _result, Value * _srcVal1)
                    : MoveInstruction(_func, _result, _srcVal1) {}

                void toString(std::string & str) override {
                    Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                    str = dstVal->getIRName() + " = *" + srcVal->getIRName();
                }
            };

            Value * tempVar = module->newVarValue(IntegerType::getTypeInt());
            LoadInstruction * loadInst = new LoadInstruction(module->getCurrentFunction(), tempVar, rightOperand);
            node->blockInsts.addInst(loadInst);
            rightOperand = tempVar;
        }

        // 创建解引用赋值指令
        class DerefMoveInstruction : public MoveInstruction {
        public:
            DerefMoveInstruction(Function * _func, Value * _result, Value * _srcVal1)
                : MoveInstruction(_func, _result, _srcVal1) {}

            void toString(std::string & str) override {
                Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                str = "*" + dstVal->getIRName() + " = " + srcVal->getIRName();
            }
        };

        MoveInstruction * movInst = new DerefMoveInstruction(module->getCurrentFunction(), left->val, rightOperand);
        node->blockInsts.addInst(movInst);

        node->val = movInst;
    } else {
        // 普通变量赋值，但需要检查右值是否是数组访问
        MoveInstruction * movInst = nullptr;

        if (son2_node->node_type == ast_operator_type::AST_OP_ARRAY_ACCESS ||
            (right->val && right->val->getType()->isPointerType() && !left->val->getType()->isPointerType())) {
            // 右值是数组访问或者右值是指针类型而左值不是指针类型，需要解引用读取
            class LoadMoveInstruction : public MoveInstruction {
            public:
                LoadMoveInstruction(Function * _func, Value * _result, Value * _srcVal1)
                    : MoveInstruction(_func, _result, _srcVal1) {}

                void toString(std::string & str) override {
                    Value *dstVal = getOperand(0), *srcVal = getOperand(1);
                    str = dstVal->getIRName() + " = *" + srcVal->getIRName();
                }
            };

            movInst = new LoadMoveInstruction(module->getCurrentFunction(), left->val, right->val);
        } else {
            // 普通赋值
            movInst = new MoveInstruction(module->getCurrentFunction(), left->val, right->val);
        }

        // 创建临时变量保存IR的值，以及线性IR指令
        node->blockInsts.addInst(right->blockInsts);
        node->blockInsts.addInst(left->blockInsts);
        node->blockInsts.addInst(movInst);

        // 这里假定赋值的类型是一致的
        node->val = movInst;
    }

    return true;
}

/// @brief return节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_return(ast_node * node)
{
    //实验七：return语句处理，确保函数只有一个出口
    Function * currentFunc = module->getCurrentFunction();
    ast_node * right = nullptr;

    // 处理返回表达式（如果有）
    if (!node->sons.empty()) {
        right = ir_visit_ast_node(node->sons[0]);
        if (!right) return false;

        node->blockInsts.addInst(right->blockInsts);
        node->blockInsts.addInst(new MoveInstruction(currentFunc, currentFunc->getReturnValue(), right->val));
        node->val = right->val;
    } else {
        node->val = nullptr;
    }

    // 跳转到函数出口
    node->blockInsts.addInst(new GotoInstruction(currentFunc, currentFunc->getExitLabel()));
    return true;
}

/// @brief 类型叶子节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_leaf_node_type(ast_node * node)
{
    // 不需要做什么，直接从节点中获取即可。

    return true;
}

/// @brief 标识符叶子节点翻译成线性中间IR，变量声明的不走这个语句
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_leaf_node_var_id(ast_node * node)
{
    Value * val;

    // 查找ID型Value
    // 变量，则需要在符号表中查找对应的值

    val = module->findVarValue(node->name);

    node->val = val;

    return true;
}

/// @brief 无符号整数字面量叶子节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_leaf_node_uint(ast_node * node)
{
    ConstInt * val;

    // 新建一个整数常量Value
    val = module->newConstInt((int32_t) node->integer_val);

    node->val = val;

    return true;
}
/// @brief 变量声明语句节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_declare_statment(ast_node * node)
{
    bool result = true;

    for (auto & child: node->sons) {
        // 遍历每个变量声明
        if (!ir_variable_declare(child)) {
            result = false;
            break;
        }
        // 收集子节点的IR指令
        node->blockInsts.addInst(child->blockInsts);
    }

    return result;
}

/// @brief 变量声明节点翻译成线性中间IR
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_variable_declare(ast_node * node)
{
    // 实验八：支持数组声明
    // 普通变量声明结构：
    //   sons[0]: type_node (类型节点)
    //   sons[1]: id_node (标识符节点)
    //   sons[2]: 可选的初始值表达式 (可能为空)
    // 数组声明结构（由前端visitVarDef创建）：
    //   sons[0]: id_node (标识符节点)
    //   sons[1..n]: dim_nodes (维度表达式节点)

    if (node->sons.size() == 1) {
        // 普通变量声明（只有一个子节点，即标识符）
        ast_node * id_node = node->sons[0];
        const std::string & name = id_node->name;
        Type * type = IntegerType::getTypeInt(); // 默认int类型

        Value * var = module->newVarValue(type, name);
        node->val = var;
        return true;
    } else if (node->sons.size() >= 2) {
        // 可能是数组声明或带初始值的普通变量声明
        ast_node * first_node = node->sons[0];

        // 检查第一个子节点是否是类型节点
        if (first_node->node_type == ast_operator_type::AST_OP_LEAF_TYPE) {
            // 普通变量声明（带类型节点）
            ast_node * type_node = node->sons[0];
            ast_node * id_node = node->sons[1];

            const std::string & name = id_node->name;
            Type * type = type_node->type;

            Value * var = module->newVarValue(type, name);
            node->val = var;

            // 处理初始值（如果有）
            if (node->sons.size() >= 3 && node->sons[2]) {
                ast_node * init_expr_node = node->sons[2];
                ast_node * rhs_node = ir_visit_ast_node(init_expr_node);
                if (!rhs_node) {
                    return false;
                }

                MoveInstruction * movInst = new MoveInstruction(
                    module->getCurrentFunction(), var, rhs_node->val);
                node->blockInsts.addInst(rhs_node->blockInsts);
                node->blockInsts.addInst(movInst);
                node->val = movInst;
            }
            return true;
        } else {
            // 检查是否是带初始化的普通变量声明的特殊情况
            // 条件：第一个子节点是变量ID，只有2个子节点，第二个是字面量0
            if (first_node->node_type == ast_operator_type::AST_OP_LEAF_VAR_ID &&
                node->sons.size() == 2 &&
                node->sons[1]->node_type == ast_operator_type::AST_OP_LEAF_LITERAL_UINT &&
                node->sons[1]->integer_val == 0) {

                // 这是 "int i = 0;" 这种形式的变量声明
                const std::string & name = first_node->name;
                Type * type = IntegerType::getTypeInt();

                Value * var = module->newVarValue(type, name);

                // 生成初始化指令
                ConstInt * initValue = module->newConstInt(0);
                MoveInstruction * movInst = new MoveInstruction(
                    module->getCurrentFunction(), var, initValue);
                node->blockInsts.addInst(movInst);

                node->val = var;
                return true;
            }

            // 数组声明：sons[0]是标识符，sons[1..]是维度
            ast_node * id_node = node->sons[0];
            const std::string & name = id_node->name;

            // 收集数组维度
            std::vector<int> dimensions;
            for (size_t i = 1; i < node->sons.size(); ++i) {
                ast_node * dim_node = ir_visit_ast_node(node->sons[i]);
                if (!dim_node || !dim_node->val) {
                    return false;
                }

                // 这里简化处理，假设维度是常量
                // 在实际实现中需要更复杂的常量求值
                if (auto constInt = dynamic_cast<ConstInt*>(dim_node->val)) {
                    dimensions.push_back(constInt->getVal());
                } else {
                    // 非常量维度，暂时不支持
                    return false;
                }

                node->blockInsts.addInst(dim_node->blockInsts);
            }

            // 创建数组类型
            ArrayType * arrayType = ArrayType::getType(IntegerType::getTypeInt(), dimensions);

            // 创建数组变量
            Value * arrayVar = module->newVarValue(arrayType, name);
            node->val = arrayVar;

            return true;
        }
    }

    return false;
}

/// @brief 数组访问节点翻译成线性中间IR，实验八新增
/// @param node AST节点
/// @return 翻译是否成功，true：成功，false：失败
bool IRGenerator::ir_array_access(ast_node * node)
{
    // 完全重写的多维数组访问处理
    // 按照Dragon IR标准：对于a[m][k]，计算 (m * 第二维大小 + k) * sizeof(element)

    Function * currentFunc = module->getCurrentFunction();
    if (!currentFunc) {
        return false;
    }

    // 收集所有维度的索引和基数组
    std::vector<Value*> indices;
    Value * baseArray = nullptr;
    std::string arrayName;

    // 递归收集所有索引
    ast_node * current = node;
    while (current->node_type == ast_operator_type::AST_OP_ARRAY_ACCESS) {
        // 处理当前维度的索引
        ast_node * index_node = current->sons[1];
        ast_node * index_result = ir_visit_ast_node(index_node);
        if (!index_result) {
            return false;
        }
        node->blockInsts.addInst(index_result->blockInsts);

        // 将索引添加到列表前面（因为我们是从内向外收集的）
        indices.insert(indices.begin(), index_result->val);

        // 移动到下一层
        current = current->sons[0];
    }

    // 现在current应该是基数组变量
    if (current->node_type == ast_operator_type::AST_OP_LEAF_VAR_ID) {
        arrayName = current->name;
        baseArray = module->findVarValue(arrayName);
        if (!baseArray) {
            return false;
        }
    } else {
        return false; // 不支持的数组访问形式
    }

    // 获取数组类型和维度信息
    // 支持两种情况：1) 真正的数组类型 2) 函数参数的指针类型（当作一维数组处理）
    std::vector<int> dimensions;

    if (baseArray->getType()->isArrayType()) {
        // 真正的数组类型
        ArrayType* arrayType = static_cast<ArrayType*>(baseArray->getType());
        dimensions = arrayType->getDimensions();

        // 支持部分索引访问（数组切片）
        // 索引数量可以小于等于数组维度数量
        if (indices.size() > dimensions.size()) {
            return false; // 索引数量不能超过数组维度
        }
    } else if (baseArray->getType()->isPointerType()) {
        // 函数参数的指针类型，当作一维数组处理
        if (indices.size() != 1) {
            return false; // 指针只支持一维访问
        }
        // 对于指针，我们不需要维度信息，直接进行指针算术
    } else {
        return false; // 不支持的类型
    }

    // 按照Dragon IR标准计算多维数组偏移
    Value * finalOffset = nullptr;

    if (indices.size() == 1) {
        // 一维数组或指针：直接使用索引
        finalOffset = indices[0];
    } else if (indices.size() == 2) {
        // 二维数组：(m * 第二维大小 + k)
        ConstInt * secondDim = module->newConstInt(dimensions[1]);

        // 计算 m * 第二维大小
        BinaryInstruction * mulInst = new BinaryInstruction(
            currentFunc,
            IRInstOperator::IRINST_OP_MUL_I,
            indices[0],  // 第一个索引 m
            secondDim,   // 第二维大小
            IntegerType::getTypeInt()
        );
        node->blockInsts.addInst(mulInst);

        // 计算 m * 第二维大小 + k
        BinaryInstruction * addInst = new BinaryInstruction(
            currentFunc,
            IRInstOperator::IRINST_OP_ADD_I,
            mulInst,
            indices[1], // 第二个索引 k
            IntegerType::getTypeInt()
        );
        node->blockInsts.addInst(addInst);

        finalOffset = addInst;
    } else if (indices.size() == 3) {
        // 三维数组：((i * dim2 + j) * dim3 + k)
        ConstInt * secondDim = module->newConstInt(dimensions[1]);
        ConstInt * thirdDim = module->newConstInt(dimensions[2]);

        // 计算 i * dim2
        BinaryInstruction * mulInst1 = new BinaryInstruction(
            currentFunc,
            IRInstOperator::IRINST_OP_MUL_I,
            indices[0],  // 第一个索引 i
            secondDim,   // 第二维大小
            IntegerType::getTypeInt()
        );
        node->blockInsts.addInst(mulInst1);

        // 计算 i * dim2 + j
        BinaryInstruction * addInst1 = new BinaryInstruction(
            currentFunc,
            IRInstOperator::IRINST_OP_ADD_I,
            mulInst1,
            indices[1], // 第二个索引 j
            IntegerType::getTypeInt()
        );
        node->blockInsts.addInst(addInst1);

        // 计算 (i * dim2 + j) * dim3
        BinaryInstruction * mulInst2 = new BinaryInstruction(
            currentFunc,
            IRInstOperator::IRINST_OP_MUL_I,
            addInst1,
            thirdDim,   // 第三维大小
            IntegerType::getTypeInt()
        );
        node->blockInsts.addInst(mulInst2);

        // 计算 (i * dim2 + j) * dim3 + k
        BinaryInstruction * addInst2 = new BinaryInstruction(
            currentFunc,
            IRInstOperator::IRINST_OP_ADD_I,
            mulInst2,
            indices[2], // 第三个索引 k
            IntegerType::getTypeInt()
        );
        node->blockInsts.addInst(addInst2);

        finalOffset = addInst2;
    } else {
        // 通用多维数组访问算法：支持任意维度
        // 使用正确的多维数组地址计算公式

        // 计算第一个索引的贡献
        int weight = 1;
        for (size_t j = 1; j < dimensions.size(); j++) {
            weight *= dimensions[j];
        }

        ConstInt * weightConst = module->newConstInt(weight);
        BinaryInstruction * mulInst = new BinaryInstruction(
            currentFunc,
            IRInstOperator::IRINST_OP_MUL_I,
            indices[0],
            weightConst,
            IntegerType::getTypeInt()
        );
        node->blockInsts.addInst(mulInst);

        finalOffset = mulInst; // 从第一个索引的贡献开始

        // 累加其余索引的贡献
        for (size_t i = 1; i < indices.size(); i++) {
            // 计算当前索引的权重：从i+1维到最后一维的乘积
            int currentWeight = 1;
            for (size_t j = i + 1; j < dimensions.size(); j++) {
                currentWeight *= dimensions[j];
            }

            // 计算当前索引的贡献：indices[i] * weight
            ConstInt * currentWeightConst = module->newConstInt(currentWeight);
            BinaryInstruction * currentMulInst = new BinaryInstruction(
                currentFunc,
                IRInstOperator::IRINST_OP_MUL_I,
                indices[i],
                currentWeightConst,
                IntegerType::getTypeInt()
            );
            node->blockInsts.addInst(currentMulInst);

            // 累加到总偏移
            BinaryInstruction * addInst = new BinaryInstruction(
                currentFunc,
                IRInstOperator::IRINST_OP_ADD_I,
                finalOffset,
                currentMulInst,
                IntegerType::getTypeInt()
            );
            node->blockInsts.addInst(addInst);

            finalOffset = addInst;
        }
    }

    // 计算字节偏移：offset * sizeof(element)
    ConstInt * elementSize = module->newConstInt(4); // int类型大小为4字节
    BinaryInstruction * byteOffsetInst = new BinaryInstruction(
        currentFunc,
        IRInstOperator::IRINST_OP_MUL_I,
        finalOffset,
        elementSize,
        IntegerType::getTypeInt()
    );
    node->blockInsts.addInst(byteOffsetInst);

    // 计算最终地址：array_base + byte_offset
    const PointerType * ptrType = PointerType::get(IntegerType::getTypeInt());
    BinaryInstruction * finalAddrInst = new BinaryInstruction(
        currentFunc,
        IRInstOperator::IRINST_OP_ADD_I,
        baseArray,
        byteOffsetInst,
        const_cast<Type*>(static_cast<const Type*>(ptrType))
    );
    node->blockInsts.addInst(finalAddrInst);

    // 判断是完整索引访问还是部分索引访问
    if (indices.size() == dimensions.size()) {
        // 完整索引访问：返回标量值的地址，用于读取或写入
        Value * addrVar = module->newVarValue(const_cast<Type*>(static_cast<const Type*>(ptrType)));
        MoveInstruction * moveAddrInst = new MoveInstruction(currentFunc, addrVar, finalAddrInst);
        node->blockInsts.addInst(moveAddrInst);
        node->val = addrVar;
    } else {
        // 部分索引访问：返回数组切片，用于传递给函数
        // 创建剩余维度的数组类型，第一维为0（表示指针）
        std::vector<int> remainingDims;
        remainingDims.push_back(0); // 第一维为0，表示这是指针

        // 添加剩余的维度（跳过已使用的索引维度，但要跳过第一个剩余维度）
        for (size_t i = indices.size() + 1; i < dimensions.size(); i++) {
            remainingDims.push_back(dimensions[i]);
        }

        ArrayType* remainingArrayType = ArrayType::getType(IntegerType::getTypeInt(), remainingDims);

        // 创建正确类型的临时变量来保存数组切片地址
        Value * arrayVar = module->newVarValue(remainingArrayType);
        MoveInstruction * moveArrayInst = new MoveInstruction(currentFunc, arrayVar, finalAddrInst);
        node->blockInsts.addInst(moveArrayInst);
        node->val = arrayVar;
    }

    return true;
}
