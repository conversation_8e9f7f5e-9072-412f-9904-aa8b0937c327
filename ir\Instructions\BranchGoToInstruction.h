#ifndef __<PERSON><PERSON>CH_GOTO_INSTRUCTION_H__
#define __<PERSON><PERSON>CH_GOTO_INSTRUCTION_H__

#include "Instruction.h"
#include "Value.h"
#include "LabelInstruction.h"

/// 条件跳转指令：cond 为真跳转至 trueLabel，否则跳转至 falseLabel
class BranchGoToInstruction : public Instruction {
public:
    BranchGoToInstruction(Function* func, Value* cond, LabelInstruction* trueLabel, LabelInstruction* falseLabel);

    void toString(std::string& str) override;

    Value* getCond() const { return cond; }
    LabelInstruction* getTrueLabel() const { return trueLabel; }
    LabelInstruction* getFalseLabel() const { return falseLabel; }

private:
    Value* cond;
    LabelInstruction* trueLabel;
    LabelInstruction* falseLabel;
};

#endif // __BRANCH_GOTO_INSTRUCTION_H__
