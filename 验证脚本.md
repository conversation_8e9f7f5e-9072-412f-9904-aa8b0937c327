# 实验八数组支持验证脚本

## 验证环境准备

```bash
cd exp07/build
# 确保编译成功
cmake --build . --parallel
```

## 第一步：基础语法解析验证

### 测试命令
```bash
./minic ../test_step1_basic_syntax.c
```

### 验证要点
- [ ] 编译器能成功解析一维数组定义：`int a[10];`
- [ ] 编译器能成功解析多维数组定义：`int b[5][3];`
- [ ] 编译器能成功解析数组访问：`a[0] = 1;`
- [ ] 编译器能成功解析多维数组访问：`b[1][2] = 5;`
- [ ] 不应该出现语法错误

### 预期输出
- 如果有语法错误，会显示错误信息
- 如果成功，应该进入后续编译阶段

## 第二步：函数参数数组验证

### 测试命令
```bash
./minic ../test_step2_function_params.c
```

### 验证要点
- [ ] 支持一维数组参数：`int func1(int arr[10])`
- [ ] 支持多维数组参数：`int func2(int matrix[][5])`
- [ ] 支持数组参数的访问：`arr[0] = 1;`
- [ ] 支持函数调用时传递数组

### 预期行为
- 根据Dragon IR规范，形参数组应该转换为第一维为0的形式
- 例如：`int matrix[][5]` → `i32 %t0[0][5]`

## 第三步：复杂数组访问验证

### 测试命令
```bash
./minic ../test_step3_complex_access.c
```

### 验证要点
- [ ] 支持变量作为数组索引：`a[i]`
- [ ] 支持表达式作为数组索引：`a[i + 1]`
- [ ] 支持嵌套数组访问：`a[b[1][0]]`
- [ ] 支持数组元素参与运算：`a[0] + b[1][2]`

## 第四步：IR生成验证

### 测试命令（如果支持IR输出）
```bash
./minic ../test_step4_ir_generation.c --output-ir
# 或者
./minic ../test_step4_ir_generation.c -ir
```

### 验证要点
查看生成的IR代码，应该包含：

#### 一维数组访问 `a[5] = 42`
```
%t1 = mul 5, 4          // 索引 × sizeof(int)
%t2 = add %l1, %t1      // 数组基址 + 偏移
*%t2 = 42               // 存储值
```

#### 二维数组访问 `b[1][2] = 99`
```
%t3 = mul 1, 4          // i × 第二维大小
%t4 = add %t3, 2        // i×cols + j
%t5 = mul %t4, 4        // 线性索引 × sizeof(int)
%t6 = add %l2, %t5      // 数组基址 + 偏移
*%t6 = 99               // 存储值
```

#### 数组声明
```
declare i32 %l1[10]     // 一维数组
declare i32 %l2[3][4]   // 二维数组
```

## 第五步：错误处理验证

### 创建错误测试文件
```c
// test_errors.c
int main() {
    int a[10];
    int b[5][3];
    
    // 这些应该能正确处理（不一定报错，取决于实现）
    a[100] = 1;  // 越界访问
    b[1] = 2;    // 维度不匹配
    
    return 0;
}
```

### 测试命令
```bash
./minic ../test_errors.c
```

## 验证检查清单

### 语法支持 ✓
- [ ] 一维数组定义：`int a[10];`
- [ ] 多维数组定义：`int b[5][3];`
- [ ] 数组元素访问：`a[i]`, `b[i][j]`
- [ ] 数组作为函数参数：`int func(int arr[], int matrix[][5])`

### IR生成 ✓
- [ ] 数组声明IR：`declare i32 %l1[10]`
- [ ] 一维数组访问IR：`mul → add → dereference`
- [ ] 多维数组降维IR：正确的降维计算
- [ ] 字节偏移计算：`index × 4`

### Dragon IR规范符合度 ✓
- [ ] 类型转换：`int` → `i32`
- [ ] 变量命名：局部变量使用`%l`前缀
- [ ] 形参数组：第一维为0（如果实现）
- [ ] 访问模式：符合`mul → add → dereference`模式

## 验证结果记录

### 成功的功能
- [ ] 基础语法解析
- [ ] 函数参数数组
- [ ] 复杂数组访问
- [ ] IR生成
- [ ] 降维处理

### 需要改进的功能
- [ ] 错误处理
- [ ] 边界检查
- [ ] 类型检查
- [ ] 性能优化

## 验证总结

完成所有验证步骤后，可以确认实验八的以下要求：

1. ✅ **支持int类型的一维数组和多维数组的定义**
2. ✅ **支持数组下标变量的访问（读和写）**
3. ✅ **AST翻译成线性IR时，实现数组的降维处理**
4. 🔄 **支持std.c中有关数组函数内置**（可选）
5. 🔄 **ARM32后端上支持数组的读写访问**（可选）

如果前三项都能正确工作，说明实验八的核心要求已经成功实现！
