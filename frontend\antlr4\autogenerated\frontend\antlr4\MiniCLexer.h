
// Generated from frontend/antlr4/MiniC.g4 by ANTLR 4.13.1

#pragma once


#include "antlr4-runtime.h"




class  MiniCLexer : public antlr4::Lexer {
public:
  enum {
    T_L_PAREN = 1, T_R_PAREN = 2, T_SEMICOLON = 3, T_L_BRACE = 4, T_R_BRACE = 5, 
    T_L_BRACKET = 6, T_R_BRACKET = 7, T_ASSIGN = 8, T_COMMA = 9, T_IF = 10, 
    T_ELSE = 11, T_WHILE = 12, T_BREAK = 13, T_CONTINUE = 14, T_ADD = 15, 
    T_SUB = 16, T_MUL = 17, T_DIV = 18, T_MOD = 19, EQUALS = 20, NOT_EQUALS = 21, 
    LT = 22, LE = 23, GT = 24, GE = 25, AND = 26, OR = 27, NOT = 28, T_RETURN = 29, 
    T_INT = 30, T_VOID = 31, T_ID = 32, T_DIGIT = 33, T_OCT_LITERAL = 34, 
    T_HEX_LITERAL = 35, WS = 36, LINE_COMMENT = 37, BLOCK_COMMENT = 38
  };

  explicit MiniCLexer(antlr4::CharStream *input);

  ~MiniCLexer() override;


  std::string getGrammarFileName() const override;

  const std::vector<std::string>& getRuleNames() const override;

  const std::vector<std::string>& getChannelNames() const override;

  const std::vector<std::string>& getModeNames() const override;

  const antlr4::dfa::Vocabulary& getVocabulary() const override;

  antlr4::atn::SerializedATNView getSerializedATN() const override;

  const antlr4::atn::ATN& getATN() const override;

  // By default the static state used to implement the lexer is lazily initialized during the first
  // call to the constructor. You can call this function if you wish to initialize the static state
  // ahead of time.
  static void initialize();

private:

  // Individual action functions triggered by action() above.

  // Individual semantic predicate functions triggered by sempred() above.

};

