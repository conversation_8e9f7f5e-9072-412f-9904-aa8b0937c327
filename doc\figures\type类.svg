<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="786px" height="268px" viewBox="-0.5 -0.5 786 268" content="&lt;mxfile host=&quot;Electron&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/16.5.1 Chrome/96.0.4664.110 Electron/16.0.7 Safari/537.36&quot; modified=&quot;2025-04-06T11:08:52.613Z&quot; version=&quot;16.5.1&quot; etag=&quot;xifjDazx5QKHMn5uCJrn&quot; type=&quot;device&quot; pages=&quot;2&quot;&gt;&lt;diagram name=&quot;Page-1&quot; id=&quot;c4acf3e9-155e-7222-9cf6-157b1a14988f&quot;&gt;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&lt;/diagram&gt;&lt;diagram id=&quot;ZGStUXdZ4-zfFXYNkeH_&quot; name=&quot;第 2 页&quot;&gt;7ZpRb5swEIB/TR4bYQwkeWzSZpvUSp1SbeveXHDBmsGRcZawXz872ARw1DVR05AlfSl32Gd89x0+TunBSbr6xNE8uWcRpj3XiVY9eNNzXQAcV/5TmkJrHNcvNTEnkdZtFDPyB5uBWrsgEc4bAwVjVJB5UxmyLMOhaOgQ52zZHPbCaHPVOYqxpZiFiNra7yQSSakduoON/jMmcWJWBsGovJMiM1jvJE9QxJY1FbztwQlnTJRX6WqCqfKe8UvxPfgUZ87Pq/we3359jP1sdndVGpvuMqXaAseZeF/TOrq/EV1ofz0Wc6z3KwrjxHxJUooyKY0TkVKpBPLyhWVipgc5Ug4TQqM7VLCFespcoPCXkcYJ4+SPHI/MZHmbC42LGyhrhNIJo4xLRcbKtapJM2VML8NxLqc9GG+ASnWHcmEehVGK5jl5Xj+cGpIiHpNszIRgqR5kdjWtrdxz4cv6T1lliyzCkRltwl/aT0moryl6xnQsny9eT2jtIRec/arQM26bopRQlVLfMI9Qhow3S38AZRZREmdSCOU2sTQ41qHCXOBVi+1/gAEqWmWeY5ZiwQs5T1uBvgZcp7inxWUtXbQqqWWK0SGdoHFleAOhvNAc7sAktJj8xkh04fLcuHSbWFbysbj0LC6/SA/EmF/QPDc0QZtNMOz7Np1gC53wQHT6Fp3TRRYKwrILnueG55tenR8JZ2DBeadcfCHz3MiEXSNzYJH5wIhywoXNc2PTg8djczJgybUzWt6S8fTxfoYeCcb6e77OpgUkzqJr1SSR0jNlCo0SKKPUzpfDVHBNBNWImizvaocPm2ArH5MQ0WsdgmdNzT9C3eRFhoQXP+rCkxL6A9/IN6v63ZvCSCsi1vP6vpaeanc2k5Rg5pQOwpHVE3oLGdKzbMFD/Mo4HWrpwBi/Zs/dTlqNJBDo8HJMkSC/mw+8jSVtbv1+qp31sP317jZNlHvSs+r9oZYhOAKmhtWmIGiZKrdtmVrDXe1yf97Bf8w7OEHYvc7BDpyg+YqGzr6wNw0BB34o6nbf9SRR3wPaTXr0XX+XDDk07X7naHeDd6O9eUZU7YsPot3u6J4h7Z2CPegc7B54J9g9cFzY7TbxScK+AXewE7ldLGQGnaPdH74X7a531EJmS9uZMiQu3ZNz6574Xse6J3bPWb5eUXEh89zIHByx57yVTLvnfNIVQlURnEB58NoZ1qXywHNavTjX2bdAGLbK4bahAxcIw/8Y9p2a2J5bA/7K6TsweP3TUQoPmBMZAPV6/JBM6N5nYWBlAhjtmQl+KxPadg6cCCMrEfr9vpUL8uwTTX7LA7ZF55ZiwjpP2+CnJIrUMuNlQgSezdEahSVHcwv3Qx3FQbtI9AbWUWzAqR/F7a+jNxzFUtz8NLeM4eYXzvD2Lw==&lt;/diagram&gt;&lt;/mxfile&gt;" style="background-color: rgb(255, 255, 255);"><defs/><g><path d="M 332 28 L 332 2 L 412 2 L 412 28" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 332 28 L 332 82 L 412 82 L 412 28" fill="#ffffff" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 332 28 L 412 28" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 15px; margin-left: 372px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">Type</div></div></div></foreignObject><text x="372" y="18" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="10px" text-anchor="middle">Type</text></switch></g><path d="M 2 208 L 2 182 L 82 182 L 82 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 2 208 L 2 262 L 82 262 L 82 208" fill="#ffffff" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 2 208 L 82 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 195px; margin-left: 42px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">VoidType</div></div></div></foreignObject><text x="42" y="198" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="10px" text-anchor="middle">VoidType</text></switch></g><path d="M 102 206.5 L 102 180.5 L 183 180.5 L 183 206.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 102 206.5 L 102 263.5 L 183 263.5 L 183 206.5" fill="#ffffff" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 102 206.5 L 183 206.5" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 194px; margin-left: 143px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">IntegerType</div></div></div></foreignObject><text x="143" y="197" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="10px" text-anchor="middle">IntegerType</text></switch></g><path d="M 202 208 L 202 182 L 283 182 L 283 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 202 208 L 202 265 L 283 265 L 283 208" fill="#ffffff" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 202 208 L 283 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 195px; margin-left: 243px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">FunctionType</div></div></div></foreignObject><text x="243" y="198" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="10px" text-anchor="middle">FunctionType</text></switch></g><path d="M 302 208 L 302 182 L 383 182 L 383 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 302 208 L 302 265 L 383 265 L 383 208" fill="#ffffff" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 302 208 L 383 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 195px; margin-left: 343px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">LabelType</div></div></div></foreignObject><text x="343" y="198" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="10px" text-anchor="middle">LabelType</text></switch></g><path d="M 412 208 L 412 182 L 493 182 L 493 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 412 208 L 412 265 L 493 265 L 493 208" fill="#ffffff" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 412 208 L 493 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 195px; margin-left: 453px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">PointerType</div></div></div></foreignObject><text x="453" y="198" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="10px" text-anchor="middle">PointerType</text></switch></g><path d="M 42 182 L 322.65 65.87" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 330.97 62.43 L 324.37 70.03 L 320.93 61.71 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 142.5 180.5 L 323.02 86.67" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 331.01 82.52 L 325.1 90.66 L 320.95 82.67 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 242.5 182 L 344.53 88.82" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 351.17 82.75 L 347.56 92.15 L 341.49 85.5 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 342.5 182 L 369.14 91.7" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 371.68 83.07 L 373.45 92.98 L 364.82 90.43 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 452.5 182 L 397.24 90.66" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 392.58 82.96 L 401.09 88.33 L 393.39 92.99 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 522 208 L 522 182 L 603 182 L 603 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 522 208 L 522 265 L 603 265 L 603 208" fill="#ffffff" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 522 208 L 603 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 195px; margin-left: 563px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">FloatType</div></div></div></foreignObject><text x="563" y="198" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="10px" text-anchor="middle">FloatType</text></switch></g><path d="M 702 208 L 702 182 L 783 182 L 783 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 702 208 L 702 265 L 783 265 L 783 208" fill="#ffffff" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 702 208 L 783 208" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 195px; margin-left: 743px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 10px; font-family: Verdana; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">ArrayType</div></div></div></foreignObject><text x="743" y="198" fill="rgb(0, 0, 0)" font-family="Verdana" font-size="10px" text-anchor="middle">ArrayType</text></switch></g><path d="M 562.5 182 L 420.43 87.6" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 412.93 82.62 L 422.92 83.85 L 417.94 91.35 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><path d="M 736.02 179.01 L 421.52 65.44" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 413.05 62.38 L 423.04 61.2 L 419.99 69.67 Z" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/><rect x="622" y="209" width="60" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 58px; height: 1px; padding-top: 224px; margin-left: 623px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">...</div></div></div></foreignObject><text x="652" y="228" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">...</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>