# -* 禁用所有默认检查
# bugprone-*启用 bugprone 开头的所有检查
# -bugprone-argument-comment 禁用 bugprone-argument-comment 的检查
Checks: "-*,\
  boost-*,\
  bugprone-*,\
  -bugprone-argument-comment,\
  -bugprone-branch-clone,\
  -bugprone-fold-init-type,\
  -bugprone-incorrect-roundings,\
  -bugprone-infinite-loop,\
  -bugprone-integer-division,\
  -bugprone-macro-parentheses,\
  -bugprone-misplaced-widening-cast,\
  -bugprone-narrowing-conversions,\
  -bugprone-not-null-terminated-result,\
  -bugprone-reserved-identifier,\
  -bugprone-signed-char-misuse,\
  -bugprone-unhandled-self-assignment,\
  -bugprone-exception-escape,\
  -bugprone-easily-swappable-parameters,\
  clang-analyzer-*,\
  -clang-analyzer-core.CallAndMessage,\
  -clang-analyzer-core.DivideZero,\
  -clang-analyzer-core.NonNullParamChecker,\
  -clang-analyzer-core.NullDereference,\
  -clang-analyzer-core.UndefinedBinaryOperatorResult,\
  -clang-analyzer-core.VLASize,\
  -clang-analyzer-core.uninitialized.ArraySubscript,\
  -clang-analyzer-core.uninitialized.Assign,\
  -clang-analyzer-core.uninitialized.Branch,\
  -clang-analyzer-cplusplus.Move,\
  -clang-analyzer-cplusplus.NewDelete,\
  -clang-analyzer-cplusplus.NewDeleteLeaks,\
  -clang-analyzer-cplusplus.PlacementNew,\
  -clang-analyzer-deadcode.DeadStores,\
  -clang-analyzer-optin.cplusplus.UninitializedObject,\
  -clang-analyzer-optin.cplusplus.VirtualCall,\
  -clang-analyzer-optin.mpi.MPI-Checker,\
  -clang-analyzer-optin.portability.UnixAPI,\
  -clang-analyzer-security.FloatLoopCounter,\
  -clang-analyzer-security.insecureAPI.DeprecatedOrUnsafeBufferHandling,\
  -clang-analyzer-security.insecureAPI.strcpy,\
  -clang-analyzer-unix.Malloc,\
  -clang-analyzer-unix.MallocSizeof,\
  -clang-analyzer-unix.MismatchedDeallocator,\
  -clang-analyzer-valist.Unterminated,\
  misc-*,\
  -misc-no-recursion,\
  -misc-non-private-member-variables-in-classes,\
  -misc-redundant-expression,\
  -misc-throw-by-value-catch-by-reference,\
  -misc-unconventional-assign-operator,\
  -misc-unused-parameters,\
  -misc-unused-using-decls,\
  -misc-include-cleaner,\
  -misc-use-anonymous-namespace,\
  -misc-const-correctness,\
  modernize-*,\
  -modernize-avoid-c-arrays,\
  -modernize-deprecated-headers,\
  -modernize-loop-convert,\
  -modernize-make-unique,\
  -modernize-pass-by-value,\
  -modernize-raw-string-literal,\
  -modernize-replace-random-shuffle,\
  -modernize-return-braced-init-list,\
  -modernize-use-auto,\
  -modernize-use-default-member-init,\
  -modernize-use-equals-delete,\
  -modernize-use-trailing-return-type,\
  -modernize-use-using,\
  -modernize-use-equals-default,\
  mpi-*,\
  openmp-*,\
  performance-*,\
  -performance-inefficient-string-concatenation,\
  -performance-no-int-to-ptr,\
  -performance-type-promotion-in-math-fn,\
  -performance-unnecessary-value-param,\
  portability-*,\
  readability-*,\
  -readability-braces-around-statements,\
  -readability-convert-member-functions-to-static,\
  -readability-else-after-return,\
  -readability-function-cognitive-complexity,\
  -readability-function-size,\
  -readability-implicit-bool-conversion,\
  -readability-inconsistent-declaration-parameter-name,\
  -readability-isolate-declaration,\
  -readability-magic-numbers,\
  -readability-make-member-function-const,\
  -readability-named-parameter,\
  -readability-non-const-parameter,\
  -readability-qualified-auto,\
  -readability-redundant-declaration,\
  -readability-redundant-preprocessor,\
  -readability-simplify-boolean-expr,\
  -readability-static-accessed-through-instance,\
  -readability-uppercase-literal-suffix,\
  -readability-use-anyofallof,\
  -readability-identifier-length,\
  -readability-avoid-unconditional-preprocessor-if,\
  -readability-redundant-access-specifiers,\
  "
# WarningsAsErrors: "*"
