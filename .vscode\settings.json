{
	"tabnine.experimentalAutoImports": true,
	"editor.fontFamily": "'FiraCode NFM',monospace,'Courier New',Menlo,Monaco",
	"editor.fontSize": 16,
	"editor.fontLigatures": true,
	"editor.linkedEditing": true,
	"editor.formatOnType": true,
	"editor.formatOnSave": true,
	"editor.formatOnPaste": true,
	"editor.tabSize": 4,
	"editor.fontVariations": false,
	"editor.detectIndentation": false,
	"editor.guides.bracketPairs": true,
	"editor.insertSpaces": false,
	"git.enableSmartCommit": true,
	"git.confirmSync": false,
	"git.autofetch": true,
	"git.ignoreLimitWarning": true,
	"git.openRepositoryInParentFolders": "always",
	"gitlens.hovers.currentLine.over": "line",
	"gitlens.defaultDateFormat": null,
	"gitlens.graph.layout": "editor",
	"gitlens.gitCommands.skipConfirmations": ["fetch:command", "switch:command", "push:command"],
	"workbench.iconTheme": "material-icon-theme",
	"workbench.colorTheme": "Visual Studio Light",
	"workbench.editor.enablePreview": false,
	"workbench.editorAssociations": {
		"*.ipynb": "jupyter-notebook",
		"*.pdf": "latex-workshop-pdf-hook"
	},
	"vsicons.dontShowNewVersionMessage": true,
	"todo-tree.general.tags": ["BUG", "HACK", "FIXME", "TODO", "XXX", "[ ]", "[x]"],
	"todo-tree.regex.regex": "(//|#|<!--|;|/\\*|^|^\\s*(-|\\d+.))\\s*($TAGS)",
	"cmake.configureOnOpen": false,
	"cmake.generator": "Ninja",
	"cmake.options.statusBarVisibility": "visible",
	"cmake.options.advanced": {
		"build": {
			"statusBarVisibility": "visible"
		},
		"launch": {
			"statusBarVisibility": "visible"
		},
		"debug": {
			"statusBarVisibility": "visible"
		}
	},
	"?cmake.languageSupport.dotnetPath": "请根据操作系统的种类进行修改,Linux：/usr/bin/dotnet,Mac：/usr/local/opt/dotnet@6/bin/dotnet,Windows：C:/Program Files/dotnet/dotnet.exe",
	"cmake.languageSupport.dotnetPath": "/usr/bin/dotnet",
	"debug.allowBreakpointsEverywhere": true,
	"explorer.confirmDelete": false,
	"terminal.integrated.defaultProfile.windows": "Command Prompt",
	"terminal.integrated.enableMultiLinePasteWarning": "auto",
	"terminal.integrated.defaultProfile.osx": "zsh",
	"terminal.integrated.fontSize": 14,
	"diffEditor.ignoreTrimWhitespace": false,
	"prettier.vueIndentScriptAndStyle": true,
	"prettier.semi": true,
	"prettier.singleQuote": true,
	"prettier.useTabs": true,
	"prettier.printWidth": 100,
	"clang-format.executable": "clang-format",
	"clang-format.style": "file",
	"clang-format.fallbackStyle": "LLVM",
	"[cpp]": {
		"editor.defaultFormatter": "xaver.clang-format"
	},
	"[jsonc]": {
		"editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"markdown.copyFiles.destination": {
		"*.md": "figs/${documentBaseName}-${fileName}"
	},
	"markdown-preview-enhanced.codeBlockTheme": "github-dark.css",
	"markdown-preview-enhanced.previewTheme": "one-dark.css",
	"markdown-preview-enhanced.imageFolderPath": "/figs",
	"antlr4.generation": {
		"mode": "external",
		"language": "Cpp",
		"outputDir": "autogenerated",
		"alternativeJar": "../../thirdparty/antlr4/antlr-4.12.0-complete.jar",
		"listeners": false,
		"visitors": true
	},
	"C_Cpp.intelliSenseEngine": "disabled",
	"C_Cpp.codeAnalysis.clangTidy.enabled": true,
	"clangd.serverCompletionRanking": true,
	"clangd.arguments": [
		// 在后台自动分析文件（基于complie_commands)
		"--background-index",
		// 标记compelie_commands.json文件的目录位置
		"--compile-commands-dir=${workspaceFolder}/build",
		// 同时开启的任务数量
		"-j=4",
		// clang-tidy功能
		"--clang-tidy",
		// 全局补全（会自动补充头文件）
		"--all-scopes-completion",
		// 详细补全
		"--completion-style=detailed",
		// 补充头文件
		"--header-insertion=iwyu",
		// pch优化的位置，如果内存足够可观注释掉这个选项
		"--pch-storage=disk"
	],
	// 作为编译失败的备选标志，指定了包含文件夹的路径
	"clangd.fallbackFlags": [
		"-I${workspaceFolder}/ir",
		"-I${workspaceFolder}/ir/Generator",
		"-I${workspaceFolder}/ir/Instructions",
		"-I${workspaceFolder}/ir/Types",
		"-I${workspaceFolder}/ir/Values",
		"-I${workspaceFolder}/frontend",
		"-I${workspaceFolder}/frontend/antlr4",
		"-I${workspaceFolder}/frontend/flexbison",
		"-I${workspaceFolder}/frontend/recursivedescent",
		"-I${workspaceFolder}/backend",
		"-I${workspaceFolder}/backend/arm32",
		"-I${workspaceFolder}/utils",
		"-I${workspaceFolder}/common",
		"-I${workspaceFolder}/symboltable"
	],

	"files.autoSave": "afterDelay",

	// Doxygen documentation generator set
	// Doxygen comment trigger. This character sequence triggers generation of Doxygen comments.
	"doxdocgen.c.triggerSequence": "///",

	"doxdocgen.c.firstLine": "///",

	// The last line of the comment that gets generated. If empty it won't get generated at all.
	"doxdocgen.c.lastLine": "///",

	// The prefix that is used for each comment line except for first and last.
	"doxdocgen.c.commentPrefix": "/// ",

	// Smart text snippet for getters.
	"doxdocgen.c.getterText": "Get the {name} object",

	// Smart text snippet for setters.
	"doxdocgen.c.setterText": "Set the {name} object",

	// Smart text snippet for constructors.
	"doxdocgen.cpp.ctorText": "Construct a new {name} object",

	// Smart text snippet for destructors.
	"doxdocgen.cpp.dtorText": "Destroy the {name} object",

	// The template of the template parameter Doxygen line(s) that are generated. If empty it won't get generated at all.
	"doxdocgen.cpp.tparamTemplate": "@tparam {param} ",

	// 文件注释：版权信息模板
	"doxdocgen.file.copyrightTag": ["@copyright Copyright (c) {year}"],
	// 文件注释：自定义模块，这里我添加一个修改日志
	"doxdocgen.file.customTag": [
		"@par 修改日志:",
		"<table>",
		"<tr><th>Date       <th>Version <th>Author  <th>Description",
		"<tr><td>{date} <td>1.0     <td>XXXXX  <td>内容",
		"</table>"
	],
	// 文件注释的组成及其排序
	"doxdocgen.file.fileOrder": [
		"file", // @file
		"brief", // @brief 简介
		"author", // 作者
		"version", // 版本
		"date", // 日期
		"empty", // 空行
		"copyright", // 版权
		"empty",
		"custom" // 自定义
	],
	// 下面时设置上面标签tag的具体信息，请根据具体情况设置
	"doxdocgen.file.fileTemplate": "@file {name}",
	"doxdocgen.file.versionTag": "@version 1.0",
	"doxdocgen.generic.authorEmail": "<EMAIL>",
	"doxdocgen.generic.authorName": "zenglj",
	"doxdocgen.generic.authorTag": "<AUTHOR> ({email})",
	// 日期格式与模板
	"doxdocgen.generic.dateFormat": "YYYY-MM-DD",
	"doxdocgen.generic.dateTemplate": "@date {date}",

	// 根据自动生成的注释模板（目前主要体现在函数注释上）
	"doxdocgen.generic.order": ["brief", "tparam", "param", "return"],
	"doxdocgen.generic.paramTemplate": "@param {param} ",
	"doxdocgen.generic.returnTemplate": "@return {type} ",
	// Substitute {author} with git config --get user.name.
	"doxdocgen.generic.useGitUserName": true,
	// Substitute {email} with git config --get user.email.
	"doxdocgen.generic.useGitUserEmail": true,
	"doxdocgen.generic.splitCasingSmartText": true,

	"VSCodeCounter.useGitignore": true,
	"VSCodeCounter.useFilesExclude": true,
	"VSCodeCounter.exclude": [
		"**/.gitignore",
		"**/.vscode/**",
		"**/node_modules/**",
		"**/tests/**",
		"**/frontend/antlr4/autogenerated/**",
		"**/frontend/flexbison/autogenerated/**",
		"**/utils/getopt-port*"
	]
}
