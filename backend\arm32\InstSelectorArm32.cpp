﻿///
/// @file InstSelectorArm32.cpp
/// @brief 指令选择器-ARM32的实现
/// <AUTHOR> (<EMAIL>)
/// @version 1.0
/// @date 2024-11-21
///
/// @copyright Copyright (c) 2024
///
/// @par 修改日志:
/// <table>
/// <tr><th>Date       <th>Version <th>Author  <th>Description
/// <tr><td>2024-11-21 <td>1.0     <td>zenglj  <td>新做
/// </table>
///
#include <cstdio>

#include "Common.h"
#include "ILocArm32.h"
#include "InstSelectorArm32.h"
#include "PlatformArm32.h"
#include <iostream>
#include "PointerType.h"
#include "RegVariable.h"
#include "Function.h"
#include "BranchGoToInstruction.h"
#include "LabelInstruction.h"
#include "GotoInstruction.h"
#include "FuncCallInstruction.h"
#include "MoveInstruction.h"

/// @brief 构造函数
/// @param _irCode 指令
/// @param _iloc ILoc
/// @param _func 函数
InstSelectorArm32::InstSelectorArm32(vector<Instruction *> & _irCode,
                                     ILocArm32 & _iloc,
                                     Function * _func,
                                     SimpleRegisterAllocator & allocator)
    : ir(_irCode), iloc(_iloc), func(_func), simpleRegisterAllocator(allocator)
{
    translator_handlers[IRInstOperator::IRINST_OP_ENTRY] = &InstSelectorArm32::translate_entry;
    translator_handlers[IRInstOperator::IRINST_OP_EXIT] = &InstSelectorArm32::translate_exit;

    translator_handlers[IRInstOperator::IRINST_OP_LABEL] = &InstSelectorArm32::translate_label;
    translator_handlers[IRInstOperator::IRINST_OP_GOTO] = &InstSelectorArm32::translate_goto;

    translator_handlers[IRInstOperator::IRINST_OP_ASSIGN] = &InstSelectorArm32::translate_assign;

    translator_handlers[IRInstOperator::IRINST_OP_ADD_I] = &InstSelectorArm32::translate_add_int32;
    translator_handlers[IRInstOperator::IRINST_OP_SUB_I] = &InstSelectorArm32::translate_sub_int32;
    translator_handlers[IRInstOperator::IRINST_OP_MUL_I] = &InstSelectorArm32::translate_mul_int32;
    translator_handlers[IRInstOperator::IRINST_OP_DIV_I] = &InstSelectorArm32::translate_div_int32;
    translator_handlers[IRInstOperator::IRINST_OP_MOD_I] = &InstSelectorArm32::translate_mod_int32;
    translator_handlers[IRInstOperator::IRINST_OP_NEG_I] = &InstSelectorArm32::translate_neg_int32;
    translator_handlers[IRInstOperator::IRINST_OP_GT_I] = &InstSelectorArm32::translate_gt_int32;
    translator_handlers[IRInstOperator::IRINST_OP_GE_I] = &InstSelectorArm32::translate_ge_int32;
    translator_handlers[IRInstOperator::IRINST_OP_LT_I] = &InstSelectorArm32::translate_lt_int32;
    translator_handlers[IRInstOperator::IRINST_OP_LE_I] = &InstSelectorArm32::translate_le_int32;
    translator_handlers[IRInstOperator::IRINST_OP_EQ_I] = &InstSelectorArm32::translate_eq_int32;
    translator_handlers[IRInstOperator::IRINST_OP_NE_I] = &InstSelectorArm32::translate_ne_int32;
    translator_handlers[IRInstOperator::IRINST_OP_BRANCH_GOTO] = &InstSelectorArm32::translate_branch_goto;

    translator_handlers[IRInstOperator::IRINST_OP_FUNC_CALL] = &InstSelectorArm32::translate_call;
    translator_handlers[IRInstOperator::IRINST_OP_ARG] = &InstSelectorArm32::translate_arg;
    /*IRINST_OP_LT_I, // 小于比较
    IRINST_OP_LE_I, // 小于等于比较
    IRINST_OP_GT_I, // 大于比较
    IRINST_OP_GE_I, // 大于等于比较
    IRINST_OP_EQ_I, // 等于比较
    IRINST_OP_NE_I, // 不等于比较*/
}

///
/// @brief 析构函数
///
InstSelectorArm32::~InstSelectorArm32()
{}

/// @brief 指令选择执行
void InstSelectorArm32::run()
{
    for (auto inst: ir) {
		//std::cout<<inst->getOp();
        // 逐个指令进行翻译
        if (!inst->isDead()) {
            translate(inst);
        }
    }
}

/// @brief 指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate(Instruction * inst)
{
    // 操作符
    IRInstOperator op = inst->getOp();

    map<IRInstOperator, translate_handler>::const_iterator pIter;
    pIter = translator_handlers.find(op);
    if (pIter == translator_handlers.end()) {
        // 没有找到，则说明当前不支持
        printf("Translate: Operator(%d) not support", (int) op);
        return;
    }

    // 开启时输出IR指令作为注释
    if (showLinearIR) {
        outputIRInstruction(inst);
    }

    (this->*(pIter->second))(inst);
}

///
/// @brief 输出IR指令
///
void InstSelectorArm32::outputIRInstruction(Instruction * inst)
{
    std::string irStr;
    inst->toString(irStr);
    if (!irStr.empty()) {
        iloc.comment(irStr);
    }
}

/// @brief NOP翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_nop(Instruction * inst)
{
    (void) inst;
    iloc.nop();
}

/// @brief Label指令指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_label(Instruction * inst)
{
    Instanceof(labelInst, LabelInstruction *, inst);

    iloc.label(labelInst->getName());
}

/// @brief goto指令指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_goto(Instruction * inst)
{
    Instanceof(gotoInst, GotoInstruction *, inst);

    // 无条件跳转
    iloc.jump(gotoInst->getTarget()->getName());
}
void InstSelectorArm32::translate_branch_goto(Instruction * inst)
{
    Instanceof(branchInst, BranchGoToInstruction *, inst);

    Value* cond = branchInst->getCond();
    LabelInstruction* trueLabel = branchInst->getTrueLabel();
    LabelInstruction* falseLabel = branchInst->getFalseLabel();

    // 1. 加载条件寄存器
    int32_t cond_reg = cond->getRegId();
    bool allocated_cond_reg = false;
    if (cond_reg == -1) {
        cond_reg = simpleRegisterAllocator.Allocate(cond);
        iloc.load_var(cond_reg, cond);
        allocated_cond_reg = true;
    }

    // 2. 比较条件寄存器与0
    iloc.inst("cmp", PlatformArm32::regName[cond_reg], "#0");

    // 3. 条件跳转，非零跳转到 trueLabel
    iloc.inst("bne", trueLabel->getName());

    // 4. 否则跳转到 falseLabel
	iloc.inst("b", falseLabel->getName());
    // 5. 释放临时寄存器
    if (allocated_cond_reg) {
        simpleRegisterAllocator.free(cond_reg);
    }
}

/// @brief 函数入口指令翻译成ARM32汇编
/// @param inst IR指令
//函数入口处理，为函数调用建立ARM32栈帧
void InstSelectorArm32::translate_entry(Instruction * inst)
{
    //获取需要保护的寄存器列表
    auto & protectedRegNo = func->getProtectedReg();
    auto & protectedRegStr = func->getProtectedRegStr();

    bool first = true;
    for (auto regno: protectedRegNo) {
        if (first) {
            protectedRegStr = PlatformArm32::regName[regno];
            first = false;
        } else if (!first) {
            protectedRegStr += "," + PlatformArm32::regName[regno];
        }
    }

    //保护调用者寄存器（入栈）
    if (!protectedRegStr.empty()) {
        iloc.inst("push", "{" + protectedRegStr + "}");
    }

    //为函数分配栈帧，包含局部变量和函数调用参数传递空间
    iloc.allocStack(func, ARM32_TMP_REG_NO);
}

/// @brief 函数出口指令翻译成ARM32汇编
/// @param inst IR指令
//函数出口处理，恢复ARM32栈帧并处理返回值
void InstSelectorArm32::translate_exit(Instruction * inst)
{
    if (inst->getOperandsNum()) {
        //处理函数返回值
        Value * retVal = inst->getOperand(0);

        //将返回值加载到R0寄存器（ARM32返回值约定）
        iloc.load_var(0, retVal);
    }

    //恢复栈指针
    iloc.inst("mov", "sp", "fp");

    //恢复调用者保护的寄存器（出栈）
    auto & protectedRegStr = func->getProtectedRegStr();
    if (!protectedRegStr.empty()) {
        iloc.inst("pop", "{" + protectedRegStr + "}");
    }

    //实验七：返回调用者（ARM32函数返回指令）
    iloc.inst("bx", "lr");
}

/// @brief 赋值指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_assign(Instruction * inst)
{
    Value * result = inst->getOperand(0);
    Value * arg1 = inst->getOperand(1);

    int32_t arg1_regId = arg1->getRegId();
    int32_t result_regId = result->getRegId();

    if (arg1_regId != -1) {
        // 寄存器 => 内存
        // 寄存器 => 寄存器

        // r8 -> rs 可能用到r9
        iloc.store_var(arg1_regId, result, ARM32_TMP_REG_NO);
    } else if (result_regId != -1) {
        // 内存变量 => 寄存器

        iloc.load_var(result_regId, arg1);
    } else {
        // 内存变量 => 内存变量

        int32_t temp_regno = simpleRegisterAllocator.Allocate();

        // arg1 -> r8
        iloc.load_var(temp_regno, arg1);

        // r8 -> rs 可能用到r9
        iloc.store_var(temp_regno, result, ARM32_TMP_REG_NO);

        simpleRegisterAllocator.free(temp_regno);
    }
}

/// @brief 二元操作指令翻译成ARM32汇编
/// @param inst IR指令
/// @param operator_name 操作码
/// @param rs_reg_no 结果寄存器号
/// @param op1_reg_no 源操作数1寄存器号
/// @param op2_reg_no 源操作数2寄存器号
void InstSelectorArm32::translate_two_operator(Instruction * inst, string operator_name)
{
    Value * result = inst;
    Value * arg1 = inst->getOperand(0);
    Value * arg2 = inst->getOperand(1);

    int32_t arg1_reg_no = arg1->getRegId();
    int32_t arg2_reg_no = arg2->getRegId();
    int32_t result_reg_no = inst->getRegId();
    int32_t load_result_reg_no, load_arg1_reg_no, load_arg2_reg_no;

    // 看arg1是否是寄存器，若是则寄存器寻址，否则要load变量到寄存器中
    if (arg1_reg_no == -1) {

        // 分配一个寄存器r8
        load_arg1_reg_no = simpleRegisterAllocator.Allocate(arg1);

        // arg1 -> r8，这里可能由于偏移不满足指令的要求，需要额外分配寄存器
        iloc.load_var(load_arg1_reg_no, arg1);
    } else {
        load_arg1_reg_no = arg1_reg_no;
    }

    // 看arg2是否是寄存器，若是则寄存器寻址，否则要load变量到寄存器中
    if (arg2_reg_no == -1) {

        // 分配一个寄存器r9
        load_arg2_reg_no = simpleRegisterAllocator.Allocate(arg2);

        // arg2 -> r9
        iloc.load_var(load_arg2_reg_no, arg2);
    } else {
        load_arg2_reg_no = arg2_reg_no;
    }

    // 看结果变量是否是寄存器，若不是则需要分配一个新的寄存器来保存运算的结果
    if (result_reg_no == -1) {
        // 分配一个寄存器r10，用于暂存结果
        load_result_reg_no = simpleRegisterAllocator.Allocate(result);
    } else {
        load_result_reg_no = result_reg_no;
    }

    // r8 + r9 -> r10
    iloc.inst(operator_name,
              PlatformArm32::regName[load_result_reg_no],
              PlatformArm32::regName[load_arg1_reg_no],
              PlatformArm32::regName[load_arg2_reg_no]);

    // 结果不是寄存器，则需要把rs_reg_name保存到结果变量中
    if (result_reg_no == -1) {

        // 这里使用预留的临时寄存器，因为立即数可能过大，必须借助寄存器才可操作。

        // r10 -> result
        iloc.store_var(load_result_reg_no, result, ARM32_TMP_REG_NO);
    }

    // 释放寄存器
    simpleRegisterAllocator.free(arg1);
    simpleRegisterAllocator.free(arg2);
    simpleRegisterAllocator.free(result);
}

/// @brief 整数加法指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_add_int32(Instruction * inst)
{
    translate_two_operator(inst, "add");
}

/// @brief 整数减法指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_sub_int32(Instruction * inst)
{
    translate_two_operator(inst, "sub");
}
/// @brief 整数乘法指令翻译成ARM32汇编
/// @param inst IR指令
void InstSelectorArm32::translate_mul_int32(Instruction * inst)
{
    translate_two_operator(inst, "mul");
}


/// @brief 整数除法指令翻译成ARM32汇编
/// @brief 整数除法指令翻译成ARM32汇编（使用 sdiv）
void InstSelectorArm32::translate_div_int32(Instruction * inst) {
    // 获取除法的结果和操作数
    Value * result = inst;
    Value * arg1 = inst->getOperand(0);
    Value * arg2 = inst->getOperand(1);

    // 获取操作数的寄存器ID
    int arg1_reg = arg1->getRegId();
    int arg2_reg = arg2->getRegId();

    // 如果被除数或除数不在寄存器中，我们需要将它们加载到寄存器中
    if (arg1_reg == -1) {
        // 将arg1从内存加载到寄存器中
        arg1_reg = simpleRegisterAllocator.Allocate(arg1);
        iloc.load_var(arg1_reg, arg1);
    }

    if (arg2_reg == -1) {
        // 将arg2从内存加载到寄存器中
        arg2_reg = simpleRegisterAllocator.Allocate(arg2);
        iloc.load_var(arg2_reg, arg2);
    }

    // 获取结果寄存器，如果没有分配寄存器，则分配一个
    int32_t result_reg = (result->getRegId() == -1)
        ? simpleRegisterAllocator.Allocate(result)
        : result->getRegId();

    // 生成ARM32汇编指令 sdiv
    iloc.inst("sdiv", PlatformArm32::regName[result_reg],
                       PlatformArm32::regName[arg1_reg],
                       PlatformArm32::regName[arg2_reg]);

    // 如果result寄存器是临时的，需要将其存储到内存
    if (result->getRegId() == -1) {
        iloc.store_var(result_reg, result, ARM32_TMP_REG_NO);
        simpleRegisterAllocator.free(result_reg);  // 释放临时寄存器
    }

    // 释放操作数和结果寄存器
    if (arg1->getRegId() == -1) {
        simpleRegisterAllocator.free(arg1_reg);  // 如果arg1是临时寄存器，释放它
    }
    if (arg2->getRegId() == -1) {
        simpleRegisterAllocator.free(arg2_reg);  // 如果arg2是临时寄存器，释放它
    }
    simpleRegisterAllocator.free(result);
}


/// @brief 整数取模指令翻译成ARM32汇编
void InstSelectorArm32::translate_mod_int32(Instruction * inst) {
    Value * result = inst;
    Value * arg1 = inst->getOperand(0); // 被除数
    Value * arg2 = inst->getOperand(1); // 除数

    int arg1_reg = arg1->getRegId();
    int arg2_reg = arg2->getRegId();
    int result_reg = result->getRegId();

    int reg_a = (arg1_reg != -1) ? arg1_reg : simpleRegisterAllocator.Allocate(arg1);
    int reg_b = (arg2_reg != -1) ? arg2_reg : simpleRegisterAllocator.Allocate(arg2);

    // 若未分配寄存器，加载变量值
    if (arg1_reg == -1) iloc.load_var(reg_a, arg1);
    if (arg2_reg == -1) iloc.load_var(reg_b, arg2);

    // 中间寄存器，直接通过现有的寄存器分配器进行管理
    int reg_q = simpleRegisterAllocator.Allocate(); // 为商分配寄存器
    int reg_t = simpleRegisterAllocator.Allocate(); // 为乘积分配寄存器

    // 商 reg_q = reg_a / reg_b
    iloc.inst("sdiv", PlatformArm32::regName[reg_q], PlatformArm32::regName[reg_a], PlatformArm32::regName[reg_b]);

    // reg_t = reg_q * reg_b
    iloc.inst("mul", PlatformArm32::regName[reg_t], PlatformArm32::regName[reg_q], PlatformArm32::regName[reg_b]);

    // reg_r = reg_a - reg_t => 余数
    int reg_r = result_reg;
    if (result_reg == -1) reg_r = simpleRegisterAllocator.Allocate(result);
    iloc.inst("sub", PlatformArm32::regName[reg_r], PlatformArm32::regName[reg_a], PlatformArm32::regName[reg_t]);

    // 如果 result 没寄存器，说明要写回内存
    if (result_reg == -1) {
        iloc.store_var(reg_r, result, ARM32_TMP_REG_NO);
        simpleRegisterAllocator.free(reg_r);
    }

    // 清理中间寄存器
    simpleRegisterAllocator.free(reg_q);  // 释放商寄存器
    simpleRegisterAllocator.free(reg_t);  // 释放乘积寄存器
    if (arg1_reg == -1) simpleRegisterAllocator.free(arg1);  // 释放被除数寄存器
    if (arg2_reg == -1) simpleRegisterAllocator.free(arg2);  // 释放除数寄存器
    simpleRegisterAllocator.free(result);  // 释放结果寄存器
}


/// @brief 整数求负指令翻译成ARM32汇编
/// @param inst IR指令
/// @brief 简化优化的整数求负指令翻译
void InstSelectorArm32::translate_neg_int32(Instruction * inst)
{
    Value * arg = inst->getOperand(1);
    Value * result = inst;

    // 1. 加载源操作数到寄存器
    int32_t arg_reg = arg->getRegId();
    bool allocated_arg_reg = false;
    if (arg_reg == -1) {
        arg_reg = simpleRegisterAllocator.Allocate(arg);
        iloc.load_var(arg_reg, arg);
        allocated_arg_reg = true;
    }

    // 2. 分配结果寄存器
    int32_t result_reg = result->getRegId();
    bool allocated_result_reg = false;
    if (result_reg == -1) {
        result_reg = simpleRegisterAllocator.Allocate(result);
        allocated_result_reg = true;
    }

    // 3. 生成取负指令（使用rsbs以设置条件标志）
    iloc.inst("rsbs",
              PlatformArm32::regName[result_reg],
              PlatformArm32::regName[arg_reg],
              "#0");

    // 4. 存储结果（如果需要）
    if (result->getMemoryAddr()) {
        iloc.store_var(result_reg, result, ARM32_TMP_REG_NO);
    }

    // 5. 释放临时寄存器
    if (allocated_arg_reg) {
        simpleRegisterAllocator.free(arg_reg);
    }
    if (allocated_result_reg) {
        simpleRegisterAllocator.free(result_reg);
    }
}
// 小于比较
void InstSelectorArm32::translate_lt_int32(Instruction * inst) {
    translate_compare(inst, "lt");
}

// 小于等于比较
void InstSelectorArm32::translate_le_int32(Instruction * inst) {
    translate_compare(inst, "le");
}

// 大于比较
void InstSelectorArm32::translate_gt_int32(Instruction * inst) {
    translate_compare(inst, "gt");
}

// 大于等于比较
void InstSelectorArm32::translate_ge_int32(Instruction * inst) {
    translate_compare(inst, "ge");
}

// 等于比较
void InstSelectorArm32::translate_eq_int32(Instruction * inst) {
    translate_compare(inst, "eq");
}

// 不等于比较
void InstSelectorArm32::translate_ne_int32(Instruction * inst) {
    translate_compare(inst, "ne");
}

// 通用比较函数
void InstSelectorArm32::translate_compare(Instruction * inst, const string & op)
{
    Value * result = inst;
    Value * arg1 = inst->getOperand(0);
    Value * arg2 = inst->getOperand(1);

    // 加载操作数
    int32_t arg1_reg = arg1->getRegId();
    if (arg1_reg == -1) {
        arg1_reg = simpleRegisterAllocator.Allocate(arg1);
        iloc.load_var(arg1_reg, arg1);
    }

    int32_t arg2_reg = arg2->getRegId();
    if (arg2_reg == -1) {
        arg2_reg = simpleRegisterAllocator.Allocate(arg2);
        iloc.load_var(arg2_reg, arg2);
    }

    // 比较操作
    iloc.inst("cmp", PlatformArm32::regName[arg1_reg],
                   PlatformArm32::regName[arg2_reg]);

    // 分配结果寄存器
    int32_t result_reg = result->getRegId();
    if (result_reg == -1) {
        result_reg = simpleRegisterAllocator.Allocate(result);
    }

    // 直接设置条件结果，避免多余的内存操作
    string cond;
    if (op == "eq") cond = "moveq";
    else if (op == "ne") cond = "movne";
    else if (op == "lt") cond = "movlt";
    else if (op == "le") cond = "movle";
    else if (op == "gt") cond = "movgt";
    else if (op == "ge") cond = "movge";

    iloc.inst("mov", PlatformArm32::regName[result_reg], "#0");
    iloc.inst(cond, PlatformArm32::regName[result_reg], "#1");

    // 释放临时寄存器
    if (arg1->getRegId() == -1) simpleRegisterAllocator.free(arg1_reg);
    if (arg2->getRegId() == -1) simpleRegisterAllocator.free(arg2_reg);
    if (result->getRegId() == -1) {
        iloc.store_var(result_reg, result, ARM32_TMP_REG_NO);
        simpleRegisterAllocator.free(result_reg);
    }

}

/// @brief 函数调用指令翻译成ARM32汇编
/// @param inst IR指令
//实验七：函数调用指令翻译，支持多个实参的ARM32汇编生成
void InstSelectorArm32::translate_call(Instruction * inst)
{
    FuncCallInstruction * callInst = dynamic_cast<FuncCallInstruction *>(inst);

    //获取函数调用的实参个数
    int32_t operandNum = callInst->getOperandsNum();

    if (operandNum != realArgCount) {

        // 两者不一致 也可能没有ARG指令，正常
        if (realArgCount != 0) {

            minic_log(LOG_ERROR, "ARG指令的个数与调用函数个数不一致");
        }
    }

    if (operandNum) {

        //强制占用ARM32前四个参数传递寄存器R0-R3
        simpleRegisterAllocator.Allocate(0);
        simpleRegisterAllocator.Allocate(1);
        simpleRegisterAllocator.Allocate(2);
        simpleRegisterAllocator.Allocate(3);

        //第5个及以后的实参采用栈传递
        int esp = 0;
        for (int32_t k = 4; k < operandNum; k++) {

            auto arg = callInst->getOperand(k);

            //为栈传递的实参创建内存变量
            MemVariable * newVal = func->newMemVariable((Type *) PointerType::get(arg->getType()));
            newVal->setMemoryAddr(ARM32_SP_REG_NO, esp);
            esp += 4;

            Instruction * assignInst = new MoveInstruction(func, newVal, arg);

            // 翻译赋值指令
            translate_assign(assignInst);

            delete assignInst;
        }

        //前4个实参通过寄存器R0-R3传递
        for (int32_t k = 0; k < operandNum && k < 4; k++) {

            auto arg = callInst->getOperand(k);

            //将实参值赋给对应的参数寄存器
            Instruction * assignInst = new MoveInstruction(func, PlatformArm32::intRegVal[k], arg);

            // 翻译赋值指令
            translate_assign(assignInst);

            delete assignInst;
        }
    }

    //生成函数调用指令
    iloc.call_fun(callInst->getName());

    if (operandNum) {
        //释放参数传递寄存器R0-R3
        simpleRegisterAllocator.free(0);
        simpleRegisterAllocator.free(1);
        simpleRegisterAllocator.free(2);
        simpleRegisterAllocator.free(3);
    }

    //处理函数返回值（通过R0寄存器）
    if (callInst->hasResultValue()) {

        //将R0寄存器的返回值赋给结果变量
        Instruction * assignInst = new MoveInstruction(func, callInst, PlatformArm32::intRegVal[0]);

        // 翻译赋值指令
        translate_assign(assignInst);

        delete assignInst;
    }

    // 函数调用后清零，使得下次可正常统计
    realArgCount = 0;
}

///
/// @brief 实参指令翻译成ARM32汇编
/// @param inst
//实验七：实参指令处理，验证ARM32调用约定的正确性
void InstSelectorArm32::translate_arg(Instruction * inst)
{
    // 翻译之前必须确保源操作数要么是寄存器，要么是内存，否则出错。
    Value * src = inst->getOperand(0);

    //获取当前实参的寄存器ID
    int32_t regId = src->getRegId();

    if (realArgCount < 4) {
        //前4个实参必须使用寄存器R0-R3传递
        if (regId != -1) {
            if (regId != realArgCount) {
                //验证寄存器分配是否正确
                minic_log(LOG_ERROR, "第%d个ARG指令对象寄存器分配有误: %d", argCount + 1, regId);
            }
        } else {
            minic_log(LOG_ERROR, "第%d个ARG指令对象不是寄存器", argCount + 1);
        }
    } else {
        //第5个及以后的实参必须使用栈传递
        int32_t baseRegId;
        bool result = src->getMemoryAddr(&baseRegId);
        if ((!result) || (baseRegId != ARM32_SP_REG_NO)) {

            minic_log(LOG_ERROR, "第%d个ARG指令对象不是SP寄存器寻址", argCount + 1);
        }
    }

    //累计实参个数
    realArgCount++;
}
