{"recommendations": ["aaron-bond.better-comments", "alefragnani.bookmarks", "alexcodercorp.comments", "christian-kohler.path-intellisense", "codezombiech.gitignore", "colejcummins.llvm-syntax-highlighting", "cschlosser.doxdocgen", "dan-c-underwood.arm", "daohong-emilio.yash", "donjayamanne.githistory", "eamodio.gitlens", "editorconfig.editorconfig", "esbenp.prettier-vscode", "formulahendry.auto-close-tag", "formulahendry.auto-rename-tag", "formulahendry.code-runner", "gera2ld.markmap-vscode", "github.remotehub", "gruntfuggly.todo-tree", "hediet.vscode-drawio", "hoovercj.vscode-power-mode", "james-yu.latex-workshop", "jebbs.plantuml", "jeff-hykin.better-cpp-syntax", "joaompinto.vscode-graphviz", "josetr.cmake-language-support-vscode", "llvm-vs-code-extensions.vscode-clangd", "mechatroner.rainbow-csv", "mike-lischke.vscode-antlr4", "ms-azuretools.vscode-docker", "ms-ceintl.vscode-language-pack-zh-hans", "ms-dotnettools.vscode-dotnet-runtime", "ms-python.debugpy", "ms-python.isort", "ms-python.python", "ms-python.vscode-pylance", "ms-toolsai.jupyter", "ms-toolsai.jupyter-keymap", "ms-toolsai.jupyter-renderers", "ms-toolsai.vscode-jupyter-cell-tags", "ms-toolsai.vscode-jupyter-slideshow", "ms-vscode-remote.remote-containers", "ms-vscode-remote.remote-ssh", "ms-vscode-remote.remote-ssh-edit", "ms-vscode-remote.remote-wsl", "ms-vscode-remote.vscode-remote-extensionpack", "ms-vscode.azure-repos", "ms-vscode.cmake-tools", "ms-vscode.cpptools", "ms-vscode.cpptools-extension-pack", "ms-vscode.cpptools-themes", "ms-vscode.hexeditor", "ms-vscode.makefile-tools", "ms-vscode.remote-explorer", "ms-vscode.remote-repositories", "ms-vscode.remote-server", "nhoizey.gremlins", "njpwerner.autodocstring", "oderwat.indent-rainbow", "pkief.material-icon-theme", "quicktype.quicktype", "revng.llvm-ir", "rti.omg-idl", "rust-lang.rust-analyzer", "ryu1kn.partial-diff", "shardulm94.trailing-spaces", "shd101wyy.markdown-preview-enhanced", "tabnine.tabnine-vscode", "tamasfe.even-better-toml", "techer.open-in-browser", "timonwong.shellcheck", "uctakeoff.vscode-counter", "vadimcn.vscode-lldb", "vincaslt.highlight-matching-tag", "vscode-icons-team.vscode-icons", "wayou.vscode-todo-highlight", "wesbos.theme-cobalt2", "wholroyd.jinja", "xaver.clang-format", "yzane.markdown-pdf", "yzhang.markdown-all-in-one", "zhuangtongfa.material-theme", "zxh404.vscode-proto3"]}