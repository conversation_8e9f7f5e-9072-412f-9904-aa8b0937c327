#include "BranchGoToInstruction.h"
#include "VoidType.h"

BranchGoToInstruction::BranchGoToInstruction(Function* func, Value* cond, LabelInstruction* trueLabel, LabelInstruction* falseLabel)
    : Instruction(func, IRInstOperator::IRINST_OP_GOTO, VoidType::getType()),
      cond(cond), trueLabel(trueLabel), falseLabel(falseLabel) {}

void BranchGoToInstruction::toString(std::string& str) {
    str = "bc " + cond->getIRName();

    auto formatLabel = [](LabelInstruction* label) {
        std::string labelStr;
        label->toString(labelStr);
        if (!labelStr.empty() && labelStr.back() == ':') {
            labelStr.pop_back(); // 移除尾部冒号
        }
        return labelStr;
    };

    str += ", label " + formatLabel(trueLabel) + ", label " + formatLabel(falseLabel);
}
