{"version": 3, "configurePresets": [{"name": "linux-config-base", "hidden": true, "generator": "Ninja", "condition": {"type": "equals", "lhs": "Linux", "rhs": "${hostSystemName}"}, "cacheVariables": {"CMAKE_EXPORT_COMPILE_COMMANDS": "ON"}, "vendor": {"microsoft.com/VisualStudioRemoteSettings/CMake/1.0": {"sourceDir": "$env{HOME}/.vs/$ms{projectDirName}"}}}, {"name": "macosx-config-base", "hidden": true, "generator": "Ninja", "toolchainFile": "CMake/macosx_clang_toolchain.cmake", "condition": {"type": "equals", "lhs": "<PERSON>", "rhs": "${hostSystemName}"}, "cacheVariables": {"CMAKE_EXPORT_COMPILE_COMMANDS": "ON"}, "vendor": {"microsoft.com/VisualStudioRemoteSettings/CMake/1.0": {"sourceDir": "$env{HOME}/.vs/$ms{projectDirName}"}}}, {"name": "linux-config-clang", "hidden": true, "toolchainFile": "CMake/linux_clang_toolchain.cmake", "inherits": "linux-config-base"}, {"name": "linux-config-gcc", "hidden": true, "toolchainFile": "CMake/linux_gcc_toolchain.cmake", "inherits": "linux-config-base"}, {"name": "cmake-build-debug-clang", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}, "inherits": "linux-config-clang"}, {"name": "cmake-build-release-clang", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}, "inherits": "linux-config-clang"}, {"name": "cmake-build-debug-gcc", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}, "inherits": "linux-config-gcc"}, {"name": "cmake-build-release-gcc", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}, "inherits": "linux-config-gcc"}, {"name": "cmake-build-debug-macosx", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Debug"}, "inherits": "macosx-config-base"}, {"name": "cmake-build-release-macosx", "binaryDir": "${sourceDir}/build/${presetName}", "cacheVariables": {"CMAKE_BUILD_TYPE": "Release"}, "inherits": "macosx-config-base"}], "buildPresets": [{"name": "linux-build-base", "hidden": true, "condition": {"type": "equals", "lhs": "Linux", "rhs": "${hostSystemName}"}, "jobs": 8}, {"name": "macosx-build-base", "hidden": true, "condition": {"type": "equals", "lhs": "<PERSON>", "rhs": "${hostSystemName}"}, "jobs": 8}, {"name": "linux-x64-debug-clang", "configurePreset": "cmake-build-debug-clang", "inherits": "linux-build-base"}, {"name": "linux-x64-release-clang", "configurePreset": "cmake-build-release-clang", "inherits": "linux-build-base"}, {"name": "linux-x64-debug-gcc", "configurePreset": "cmake-build-debug-gcc", "inherits": "linux-build-base"}, {"name": "linux-x64-release-gcc", "configurePreset": "cmake-build-release-gcc", "inherits": "linux-build-base"}, {"name": "macosx-x64-debug-clang", "configurePreset": "cmake-build-debug-macosx", "inherits": "macosx-build-base"}, {"name": "macosx-x64-release-clang", "configurePreset": "cmake-build-release-macosx", "inherits": "macosx-build-base"}]}