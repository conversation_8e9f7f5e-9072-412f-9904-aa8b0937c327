#pragma once

#include "Instruction.h"
#include "Value.h"
#include "Function.h"
#include "IRConstant.h"

class StoreInstruction : public Instruction {
public:
    StoreInstruction(Function* _func, Value* value, Value* addr)
        : Instruction(_func, IRInstOperator::IRINST_OP_STORE, VoidType::getType()) {
        addOperand(value);
        addOperand(addr);
    }

    void toString(std::string& str) override {
        Value* value = getOperand(0);
        Value* addr = getOperand(1);
        str = "*" + addr->getIRName() + " = " + value->getIRName();
    }
}; 