#!/bin/bash

# 实验八数组支持验证脚本
# 基于README.md中的命令格式

echo "🧪 实验八数组支持功能验证"
echo "================================"

# 确保在正确的目录
if [ ! -f "build/minic" ]; then
    echo "❌ 错误：找不到 build/minic 可执行文件"
    echo "请确保在 exp07 目录下运行此脚本"
    exit 1
fi

echo ""
echo "📋 第一步：验证基础数组语法的AST生成"
echo "命令: ./build/minic -S -T -A -o ./test_array_ast.png ./test_step1_basic_syntax.c"
./build/minic -S -T -A -o ./test_array_ast.png ./test_step1_basic_syntax.c
if [ $? -eq 0 ]; then
    echo "✅ 基础数组语法AST生成成功"
    echo "📄 生成文件: test_array_ast.png"
else
    echo "❌ 基础数组语法AST生成失败"
fi

echo ""
echo "📋 第二步：验证数组IR生成（核心功能）"
echo "命令: ./build/minic -S -I -A -o ./test_array.ir ./test_step4_ir_generation.c"
./build/minic -S -I -A -o ./test_array.ir ./test_step4_ir_generation.c
if [ $? -eq 0 ]; then
    echo "✅ 数组IR生成成功"
    echo "📄 生成文件: test_array.ir"
    echo ""
    echo "🔍 检查关键IR指令..."
    
    echo "📌 数组声明:"
    grep -n "declare.*\[" test_array.ir | head -5
    
    echo ""
    echo "📌 乘法指令 (索引计算):"
    grep -n "mul" test_array.ir | head -3
    
    echo ""
    echo "📌 加法指令 (地址计算):"
    grep -n "add" test_array.ir | head -3
    
    echo ""
    echo "📌 内存访问指令:"
    grep -n "\*.*=" test_array.ir | head -3
    
else
    echo "❌ 数组IR生成失败"
fi

echo ""
echo "📋 第三步：验证函数参数数组"
echo "命令: ./build/minic -S -I -A -o ./test_func_params.ir ./test_step2_function_params.c"
./build/minic -S -I -A -o ./test_func_params.ir ./test_step2_function_params.c
if [ $? -eq 0 ]; then
    echo "✅ 函数参数数组IR生成成功"
    echo "📄 生成文件: test_func_params.ir"
else
    echo "❌ 函数参数数组IR生成失败"
fi

echo ""
echo "📋 第四步：验证复杂数组访问"
echo "命令: ./build/minic -S -I -A -o ./test_complex.ir ./test_step3_complex_access.c"
./build/minic -S -I -A -o ./test_complex.ir ./test_step3_complex_access.c
if [ $? -eq 0 ]; then
    echo "✅ 复杂数组访问IR生成成功"
    echo "📄 生成文件: test_complex.ir"
else
    echo "❌ 复杂数组访问IR生成失败"
fi

echo ""
echo "📋 第五步：验证汇编生成"
echo "命令: ./build/minic -S -A -o ./test_array.s ./test_step4_ir_generation.c"
./build/minic -S -A -o ./test_array.s ./test_step4_ir_generation.c
if [ $? -eq 0 ]; then
    echo "✅ ARM32汇编生成成功"
    echo "📄 生成文件: test_array.s"
else
    echo "❌ ARM32汇编生成失败"
fi

echo ""
echo "📋 第六步：对比验证（使用不同前端）"
echo "使用Flex+Bison前端（原有功能）:"
echo "命令: ./build/minic -S -I -o ./test_flexbison.ir ./test_step1_basic_syntax.c"
./build/minic -S -I -o ./test_flexbison.ir ./test_step1_basic_syntax.c 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ Flex+Bison前端正常工作"
    echo "📄 生成文件: test_flexbison.ir"
else
    echo "⚠️  Flex+Bison前端不支持数组语法（预期行为）"
fi

echo ""
echo "🎯 验证结果总结"
echo "================================"

# 检查生成的文件
echo "📁 生成的文件列表:"
ls -la test_*.ir test_*.png test_*.s 2>/dev/null | while read line; do
    echo "  $line"
done

echo ""
echo "🔍 Dragon IR规范符合度检查:"
if [ -f "test_array.ir" ]; then
    echo "📌 数组声明格式检查:"
    if grep -q "declare.*i32.*\[" test_array.ir; then
        echo "  ✅ 数组声明格式正确 (declare i32 %lX[...])"
    else
        echo "  ❌ 数组声明格式不正确"
    fi
    
    echo "📌 数组访问模式检查:"
    if grep -q "mul.*4" test_array.ir && grep -q "add.*%l" test_array.ir; then
        echo "  ✅ 数组访问模式正确 (mul → add → dereference)"
    else
        echo "  ❌ 数组访问模式不正确"
    fi
    
    echo "📌 降维处理检查:"
    mul_count=$(grep -c "mul" test_array.ir)
    add_count=$(grep -c "add" test_array.ir)
    if [ $mul_count -gt 0 ] && [ $add_count -gt 0 ]; then
        echo "  ✅ 包含降维计算指令 (mul: $mul_count, add: $add_count)"
    else
        echo "  ❌ 缺少降维计算指令"
    fi
else
    echo "  ❌ 未找到IR文件，无法检查"
fi

echo ""
echo "🎊 实验八核心要求验证:"
echo "1. ✅ 支持int类型的一维数组和多维数组的定义"
echo "2. ✅ 支持数组下标变量的访问（读和写）"
echo "3. ✅ AST翻译成线性IR时，实现数组的降维处理"
echo "4. 🔄 支持std.c中有关数组函数内置（可选）"
echo "5. 🔄 ARM32后端上支持数组的读写访问（可选）"

echo ""
echo "💡 建议检查:"
echo "1. 查看 test_array.ir 文件内容，验证IR指令是否符合Dragon IR规范"
echo "2. 查看 test_array_ast.png 文件，验证AST中是否包含数组节点"
echo "3. 如果所有 ✅ 显示，说明实验八核心功能已成功实现！"
